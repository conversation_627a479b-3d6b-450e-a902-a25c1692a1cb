/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#ifndef __cplusplus
#error This file must be compiled as Obj-C++. If you are importing it, you must change your file extension to .mm.
#endif

// Avoid multiple includes of RNMapsSpecs symbols
#ifndef RNMapsSpecs_H
#define RNMapsSpecs_H

#import <Foundation/Foundation.h>
#import <RCTRequired/RCTRequired.h>
#import <RCTTypeSafety/RCTConvertHelpers.h>
#import <RCTTypeSafety/RCTTypedModuleConstants.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTCxxConvert.h>
#import <React/RCTManagedPointer.h>
#import <ReactCommon/RCTTurboModule.h>
#import <optional>
#import <vector>


NS_ASSUME_NONNULL_BEGIN
namespace JS {
  namespace NativeAirMapsModule {
    struct LatLng {
      double latitude() const;
      double longitude() const;

      LatLng(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeAirMapsModule_LatLng)
+ (RCTManagedPointer *)JS_NativeAirMapsModule_LatLng:(id)json;
@end
namespace JS {
  namespace NativeAirMapsModule {
    struct Point {
      double x() const;
      double y() const;

      Point(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeAirMapsModule_Point)
+ (RCTManagedPointer *)JS_NativeAirMapsModule_Point:(id)json;
@end
@protocol NativeAirMapsModuleSpec <RCTBridgeModule, RCTTurboModule>

- (void)getCamera:(double)tag
          resolve:(RCTPromiseResolveBlock)resolve
           reject:(RCTPromiseRejectBlock)reject;
- (void)getMarkersFrames:(double)tag
             onlyVisible:(BOOL)onlyVisible
                 resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject;
- (void)getMapBoundaries:(double)tag
                 resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject;
- (void)takeSnapshot:(double)tag
              config:(NSString *)config
             resolve:(RCTPromiseResolveBlock)resolve
              reject:(RCTPromiseRejectBlock)reject;
- (void)getAddressFromCoordinates:(double)tag
                       coordinate:(JS::NativeAirMapsModule::LatLng &)coordinate
                          resolve:(RCTPromiseResolveBlock)resolve
                           reject:(RCTPromiseRejectBlock)reject;
- (void)getPointForCoordinate:(double)tag
                   coordinate:(JS::NativeAirMapsModule::LatLng &)coordinate
                      resolve:(RCTPromiseResolveBlock)resolve
                       reject:(RCTPromiseRejectBlock)reject;
- (void)getCoordinateForPoint:(double)tag
                        point:(JS::NativeAirMapsModule::Point &)point
                      resolve:(RCTPromiseResolveBlock)resolve
                       reject:(RCTPromiseRejectBlock)reject;

@end

@interface NativeAirMapsModuleSpecBase : NSObject {
@protected
facebook::react::EventEmitterCallback _eventEmitterCallback;
}
- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper;


@end

namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeAirMapsModule'
   */
  class JSI_EXPORT NativeAirMapsModuleSpecJSI : public ObjCTurboModule {
  public:
    NativeAirMapsModuleSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
inline double JS::NativeAirMapsModule::LatLng::latitude() const
{
  id const p = _v[@"latitude"];
  return RCTBridgingToDouble(p);
}
inline double JS::NativeAirMapsModule::LatLng::longitude() const
{
  id const p = _v[@"longitude"];
  return RCTBridgingToDouble(p);
}
inline double JS::NativeAirMapsModule::Point::x() const
{
  id const p = _v[@"x"];
  return RCTBridgingToDouble(p);
}
inline double JS::NativeAirMapsModule::Point::y() const
{
  id const p = _v[@"y"];
  return RCTBridgingToDouble(p);
}
NS_ASSUME_NONNULL_END
#endif // RNMapsSpecs_H
