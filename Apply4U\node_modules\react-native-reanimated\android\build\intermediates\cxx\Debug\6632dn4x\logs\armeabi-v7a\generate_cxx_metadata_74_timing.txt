# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 44ms
  [gap of 14ms]
  write-metadata-json-to-file 23ms
generate_cxx_metadata completed in 101ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 37ms
  [gap of 12ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 91ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 17ms
  [gap of 20ms]
generate_cxx_metadata completed in 40ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 37ms
  [gap of 13ms]
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 37ms
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 81ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 38ms
  [gap of 14ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 95ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 64ms
  [gap of 14ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 123ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 37ms
  [gap of 32ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 106ms

