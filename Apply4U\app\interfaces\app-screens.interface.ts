import {IScreen} from './screen.interface';

export interface IAppScreens {
  Default: string;
  LogoutDrawerLabel: string;
  JobDetail: IScreen;
  JobSearchResults: IScreen;
  JobSearchFilter: IScreen;
  RegisterStepOne: IScreen;
  RegisterStepTwo: IScreen;
  RegisterStepThree: IScreen;
  RegisterStepFour: IScreen;
  RegisterStepFive: IScreen;
  Dashboard: IScreen;
  ForgotPassword: IScreen;
  Home: IScreen;
  Login: IScreen;
  Notifications: IScreen;
  Pricing: IScreen;
  IntroSlider: IScreen;
  ApplyNow: IScreen;
  ApplySuccessful: IScreen;
  HiddenJobs:IScreen;
  MyJobs:IScreen;
  ShortlistedJobs:IScreen;
  Browser:IScreen;
  Settings:IScreen;
  [key: string]: IScreen | string;
}
