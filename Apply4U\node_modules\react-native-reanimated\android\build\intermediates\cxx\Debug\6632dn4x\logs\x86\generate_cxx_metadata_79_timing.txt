# C/C++ build system timings
generate_cxx_metadata
  [gap of 26ms]
  create-invalidation-state 62ms
  [gap of 32ms]
generate_cxx_metadata completed in 120ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
  [gap of 33ms]
generate_cxx_metadata completed in 51ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 29ms
generate_cxx_metadata completed in 52ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 10ms
  [gap of 11ms]
generate_cxx_metadata completed in 23ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 45ms
  [gap of 16ms]
generate_cxx_metadata completed in 79ms

