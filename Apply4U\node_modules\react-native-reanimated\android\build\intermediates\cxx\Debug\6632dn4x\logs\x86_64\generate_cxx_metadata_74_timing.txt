# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 15ms
  [gap of 16ms]
generate_cxx_metadata completed in 34ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 37ms
  [gap of 11ms]
  write-metadata-json-to-file 25ms
generate_cxx_metadata completed in 93ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 110ms
  [gap of 24ms]
generate_cxx_metadata completed in 159ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 85ms
  [gap of 30ms]
generate_cxx_metadata completed in 149ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 42ms
  [gap of 11ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 93ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 21ms
  [gap of 26ms]
generate_cxx_metadata completed in 49ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 44ms
  [gap of 12ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 95ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 29ms
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 71ms

