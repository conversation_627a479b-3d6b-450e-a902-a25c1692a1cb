# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 43ms
  [gap of 20ms]
generate_cxx_metadata completed in 91ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 17ms
  [gap of 17ms]
generate_cxx_metadata completed in 38ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 33ms
  [gap of 21ms]
generate_cxx_metadata completed in 67ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 15ms
  [gap of 14ms]
generate_cxx_metadata completed in 31ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 34ms
  [gap of 16ms]
generate_cxx_metadata completed in 68ms

