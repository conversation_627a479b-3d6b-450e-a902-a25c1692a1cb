import React from 'react';

import {
  createDrawer<PERSON>avigator,
  DrawerContentComponentProps,
} from '@react-navigation/drawer';
import { useSelector } from 'react-redux';

import {
  DashboardContainer,
  ForgotPasswordContainer,
  JobSearchFilterContainer,
  LoginContainer,
  RegisterStepOneContainer,
  RegisterStepThreeContainer,
  RegisterStepTwoContainer,
  JobSearchResultsContainer,
  HomeContainer,
  JobDetailContainer,
  NotificationsContainer,
  RegisterStepFourContainer,
  RegisterStepFiveContainer,
  PricingContainer,
  IntroSliderContainer,
  ApplyNowContainer,
  ApplySuccessfulContainer,
  HiddenJobsContainer,
  MyJobsContainer,
  ShortlistedJobsContainer,
  BrowserContainer,
  MapContainer
} from '../components';

import { DrawerContentComponent } from './drawer-content.component';
import { screens } from '../app.constant';
import { isAuthenticated } from '../utility';
import { IApplicationState } from '../redux';
import { SettingsContainer } from '../components/user/settings/settings.container';

const Drawer = createDrawerNavigator();
const DrawerNavigator = () => {
  const isFirstVisit = useSelector(
    (state: IApplicationState) => state.isFirstVisit,
  );

  const initialScreen = isFirstVisit
    ? screens.IntroSlider.screen
    : isAuthenticated()
      ? screens.Dashboard.screen
      : screens.Login.screen;
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerPosition: 'right',
      }}
      drawerContent={(props: DrawerContentComponentProps) => {
        return <DrawerContentComponent {...props} />;
      }}
      backBehavior={'history'}
      initialRouteName={initialScreen}>
      <Drawer.Screen
        name={screens.Home.screen}
        options={{
          drawerLabel: screens.Home.drawerLabel,
        }}
        component={HomeContainer}
      />
      <Drawer.Screen
        name={screens.Dashboard.screen}
        options={{
          drawerLabel: screens.Dashboard.drawerLabel,
          unmountOnBlur: true,
        }}
        component={DashboardContainer}
      />
      <Drawer.Screen
        name={screens.JobSearchResults.screen}
        options={{
          drawerLabel: screens.JobSearchResults.drawerLabel,
        }}
        component={JobSearchResultsContainer}
      />

      <Drawer.Screen
        name={screens.Notifications.screen}
        options={{
          drawerLabel: screens.Notifications.drawerLabel,
          unmountOnBlur:true
        }}
        component={NotificationsContainer}
      />

      <Drawer.Screen
        name={screens.HiddenJobs.screen}
        options={{
          drawerLabel: screens.HiddenJobs.drawerLabel,
          unmountOnBlur: true,
        }}
        component={HiddenJobsContainer}
      />

      <Drawer.Screen
        name={screens.MyJobs.screen}
        options={{
          drawerLabel: screens.MyJobs.drawerLabel,
          unmountOnBlur: true,
        }}
        component={MyJobsContainer}
      />

<Drawer.Screen
        name={screens.ShortlistedJobs.screen}
        options={{
          drawerLabel: screens.ShortlistedJobs.drawerLabel,
          unmountOnBlur: true,
        }}
        component={ShortlistedJobsContainer}
      />

    <Drawer.Screen
        name={screens.Settings.screen}
        options={{
          drawerLabel: screens.Settings.drawerLabel,
        }}
        component={SettingsContainer}
      />

      <Drawer.Screen
        name={screens.Login.screen}
        options={{
          drawerLabel: screens.Login.drawerLabel,
        }}
        component={LoginContainer}
      />

      <Drawer.Screen
        name={screens.JobDetail.screen}
        options={{
          drawerLabel: screens.JobDetail.drawerLabel,
          unmountOnBlur:true,
        }}
        component={JobDetailContainer}
      />

      <Drawer.Screen
        name={screens.JobSearchFilter.screen}
        options={{
          drawerLabel: screens.JobSearchFilter.drawerLabel,
          unmountOnBlur:true,
        }}
        component={JobSearchFilterContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepOne.screen}
        options={{
          drawerLabel: screens.RegisterStepOne.drawerLabel,
        }}
        component={RegisterStepOneContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepTwo.screen}
        options={{
          drawerLabel: screens.RegisterStepTwo.drawerLabel,
        }}
        component={RegisterStepTwoContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepThree.screen}
        options={{
          drawerLabel: screens.RegisterStepThree.drawerLabel,
        }}
        component={RegisterStepThreeContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepFour.screen}
        options={{
          drawerLabel: screens.RegisterStepFour.drawerLabel,
        }}
        component={RegisterStepFourContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepFive.screen}
        options={{
          drawerLabel: screens.RegisterStepFive.drawerLabel,
        }}
        component={RegisterStepFiveContainer}
      />
      <Drawer.Screen
        name={screens.ForgotPassword.screen}
        options={{
          drawerLabel: screens.ForgotPassword.drawerLabel,
          unmountOnBlur: true,
        }}
        component={ForgotPasswordContainer}
      />
      <Drawer.Screen
        name={screens.Pricing.screen}
        options={{
          drawerLabel: screens.Pricing.drawerLabel,
        }}
        component={PricingContainer}
      />

      <Drawer.Screen
        name={screens.IntroSlider.screen}
        options={{
          drawerLabel: screens.IntroSlider.drawerLabel,
        }}
        component={IntroSliderContainer}
      />

      <Drawer.Screen
        name={screens.ApplyNow.screen}
        options={{
          drawerLabel: screens.ApplyNow.drawerLabel,
          unmountOnBlur: true,
        }}
        component={ApplyNowContainer}
      />
      <Drawer.Screen
        name={screens.ApplySuccessful.screen}
        options={{
          drawerLabel: screens.ApplySuccessful.drawerLabel,
          unmountOnBlur: true,
        }}
        component={ApplySuccessfulContainer}
      />
      <Drawer.Screen
        name={screens.Browser.screen}
        options={{
          drawerLabel: screens.Browser.drawerLabel,
          unmountOnBlur: true,
        }}
        component={BrowserContainer}
      />
      <Drawer.Screen
        name={screens.Map.screen}
        options={{
          drawerLabel: screens.Map.drawerLabel,
          unmountOnBlur: true,
        }}
        component={MapContainer}
      />
    </Drawer.Navigator>
  );
};

export { DrawerNavigator };
