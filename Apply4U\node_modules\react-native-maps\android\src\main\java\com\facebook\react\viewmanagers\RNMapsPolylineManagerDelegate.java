/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ColorPropConverter;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class RNMapsPolylineManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMapsPolylineManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNMapsPolylineManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "coordinates":
        mViewManager.setCoordinates(view, (ReadableArray) value);
        break;
      case "geodesic":
        mViewManager.setGeodesic(view, value == null ? false : (boolean) value);
        break;
      case "lineCap":
        mViewManager.setLineCap(view, (String) value);
        break;
      case "lineDashPattern":
        mViewManager.setLineDashPattern(view, (ReadableArray) value);
        break;
      case "lineJoin":
        mViewManager.setLineJoin(view, (String) value);
        break;
      case "strokeColor":
        mViewManager.setStrokeColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "strokeColors":
        mViewManager.setStrokeColors(view, (ReadableArray) value);
        break;
      case "strokeWidth":
        mViewManager.setStrokeWidth(view, value == null ? 0f : ((Double) value).floatValue());
        break;
      case "tappable":
        mViewManager.setTappable(view, value == null ? false : (boolean) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
