/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateStateH.js
 */
#pragma once

#ifdef ANDROID
#include <folly/dynamic.h>
#endif

namespace facebook::react {

class RNMapsCalloutState {
public:
  RNMapsCalloutState() = default;

#ifdef ANDROID
  RNMapsCalloutState(RNMapsCalloutState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNMapsCircleState {
public:
  RNMapsCircleState() = default;

#ifdef ANDROID
  RNMapsCircleState(RNMapsCircleState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNMapsGoogleMapViewState {
public:
  RNMapsGoogleMapViewState() = default;

#ifdef ANDROID
  RNMapsGoogleMapViewState(RNMapsGoogleMapViewState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNMapsGooglePolygonState {
public:
  RNMapsGooglePolygonState() = default;

#ifdef ANDROID
  RNMapsGooglePolygonState(RNMapsGooglePolygonState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNMapsMapViewState {
public:
  RNMapsMapViewState() = default;

#ifdef ANDROID
  RNMapsMapViewState(RNMapsMapViewState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNMapsMarkerState {
public:
  RNMapsMarkerState() = default;

#ifdef ANDROID
  RNMapsMarkerState(RNMapsMarkerState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNMapsOverlayState {
public:
  RNMapsOverlayState() = default;

#ifdef ANDROID
  RNMapsOverlayState(RNMapsOverlayState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNMapsPolylineState {
public:
  RNMapsPolylineState() = default;

#ifdef ANDROID
  RNMapsPolylineState(RNMapsPolylineState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNMapsUrlTileState {
public:
  RNMapsUrlTileState() = default;

#ifdef ANDROID
  RNMapsUrlTileState(RNMapsUrlTileState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNMapsWMSTileState {
public:
  RNMapsWMSTileState() = default;

#ifdef ANDROID
  RNMapsWMSTileState(RNMapsWMSTileState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

} // namespace facebook::react