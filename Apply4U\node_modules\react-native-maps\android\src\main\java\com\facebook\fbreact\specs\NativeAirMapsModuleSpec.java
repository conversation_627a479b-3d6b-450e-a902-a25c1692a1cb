
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJavaSpec.js
 *
 * @nolint
 */

package com.facebook.fbreact.specs;

import com.facebook.proguard.annotations.DoNotStrip;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.turbomodule.core.interfaces.TurboModule;
import javax.annotation.Nonnull;

public abstract class NativeAirMapsModuleSpec extends ReactContextBaseJavaModule implements TurboModule {
  public static final String NAME = "RNMapsAirModule";

  public NativeAirMapsModuleSpec(ReactApplicationContext reactContext) {
    super(reactContext);
  }

  @Override
  public @Nonnull String getName() {
    return NAME;
  }

  @ReactMethod
  @DoNotStrip
  public abstract void getCamera(double tag, Promise promise);

  @ReactMethod
  @DoNotStrip
  public abstract void getMarkersFrames(double tag, boolean onlyVisible, Promise promise);

  @ReactMethod
  @DoNotStrip
  public abstract void getMapBoundaries(double tag, Promise promise);

  @ReactMethod
  @DoNotStrip
  public abstract void takeSnapshot(double tag, String config, Promise promise);

  @ReactMethod
  @DoNotStrip
  public abstract void getAddressFromCoordinates(double tag, ReadableMap coordinate, Promise promise);

  @ReactMethod
  @DoNotStrip
  public abstract void getPointForCoordinate(double tag, ReadableMap coordinate, Promise promise);

  @ReactMethod
  @DoNotStrip
  public abstract void getCoordinateForPoint(double tag, ReadableMap point, Promise promise);
}
