/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ReadableMap;

public interface RNMapsCircleManagerInterface<T extends View> {
  void setCenter(T view, @Nullable ReadableMap value);
  void setFillColor(T view, @Nullable Integer value);
  void setRadius(T view, double value);
  void setStrokeColor(T view, @Nullable Integer value);
  void setStrokeWidth(T view, float value);
  void setTappable(T view, boolean value);
}
