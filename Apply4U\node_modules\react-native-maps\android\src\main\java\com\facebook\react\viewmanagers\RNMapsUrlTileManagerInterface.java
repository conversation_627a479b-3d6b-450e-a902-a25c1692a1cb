/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;

public interface RNMapsUrlTileManagerInterface<T extends View> {
  void setDoubleTileSize(T view, boolean value);
  void setFlipY(T view, boolean value);
  void setMaximumNativeZ(T view, int value);
  void setMaximumZ(T view, int value);
  void setMinimumZ(T view, int value);
  void setOfflineMode(T view, boolean value);
  void setShouldReplaceMapContent(T view, boolean value);
  void setTileCacheMaxAge(T view, int value);
  void setTileCachePath(T view, @Nullable String value);
  void setTileSize(T view, int value);
  void setUrlTemplate(T view, @Nullable String value);
}
