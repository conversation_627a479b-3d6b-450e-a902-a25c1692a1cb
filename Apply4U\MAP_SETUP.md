# Map Functionality Setup

## Overview
This document describes the map functionality that has been added to the Apply4U React Native app.

## Features Added

### 1. Map Screen
- **Location**: `app/components/user/map/`
- **Components**: 
  - `map.view.tsx` - Map view component with Google Maps integration
  - `map.container.tsx` - Container component handling navigation logic

### 2. Navigation Integration
- Added Map screen to app navigation system
- Updated `app.constant.ts` with Map screen configuration
- Updated `interfaces/app-screens.interface.ts` with Map screen type
- Added Map screen to drawer navigator

### 3. Login Page Enhancement
- Added "Go to Map" button to the login page
- <PERSON><PERSON> appears below the login button
- Navigates directly to the map screen

## Dependencies
- **react-native-maps**: Added for map functionality
- Installed with `--legacy-peer-deps` flag due to React version compatibility

## Configuration Required

### Google Maps API Key
To use Google Maps on Android, you need to:

1. Get a Google Maps API key from Google Cloud Console
2. Replace `YOUR_GOOGLE_MAPS_API_KEY_HERE` in `android/app/src/main/AndroidManifest.xml` with your actual API key

### Permissions
The following permissions are already configured in AndroidManifest.xml:
- `ACCESS_FINE_LOCATION`
- `ACCESS_COARSE_LOCATION`

## Map Features
- Shows user location
- Interactive map with zoom and scroll
- Sample marker at default location (San Francisco)
- Back button to return to previous screen

## Usage
1. Open the app
2. Go to the Login screen
3. Click "Go to Map" button
4. The map screen will open with Google Maps integration

## Technical Notes
- Uses `PROVIDER_GOOGLE` for consistent map rendering
- Map component renamed to `RNMapView` to avoid conflicts with React Native's MapView
- Screen component named `MapScreenView` for clarity
- Includes proper TypeScript interfaces and styling
