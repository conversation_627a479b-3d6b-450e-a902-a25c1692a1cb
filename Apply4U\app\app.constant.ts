import {IAppScreens, IDropDownDataSource} from './interfaces';

export const screens: IAppScreens = {
  Default: 'Dashboard',
  LogoutDrawerLabel: 'Logout',
  JobDetail: {
    screen: 'JobDetail',
    showOnDrawer: false,
    drawerLabel: 'Job Detail',
    isAuthenticationRequired: false,
  },
  JobSearchResults: {
    screen: 'JobSearchResults',
    showOnDrawer: false,
    drawerLabel: 'Search Results',
    isAuthenticationRequired: false,
  },
  JobSearchFilter: {
    screen: 'JobSearchFilter',
    showOnDrawer: true,
    drawerLabel: 'Search',
    isAuthenticationRequired: true,
  },
  RegisterStepOne: {
    screen: 'RegisterStepOne',
    showOnDrawer: false,
    drawerLabel: 'Register Step One',
    isAuthenticationRequired: false,
  },
  RegisterStepTwo: {
    screen: 'RegisterStepTwo',
    showOnDrawer: false,
    drawerLabel: 'Register Step Two',
    isAuthenticationRequired: false,
  },
  RegisterStepThree: {
    screen: 'RegisterStepThree',
    showOnDrawer: false,
    drawerLabel: 'Register Step Three',
    isAuthenticationRequired: false,
  },
  RegisterStepFour: {
    screen: 'RegisterStepFour',
    showOnDrawer: false,
    drawerLabel: 'Register Step Four',
    isAuthenticationRequired: false,
  },
  RegisterStepFive: {
    screen: 'RegisterStepFive',
    showOnDrawer: false,
    drawerLabel: 'Register Step Five',
    isAuthenticationRequired: false,
  },
  Dashboard: {
    screen: 'Dashboard',
    showOnDrawer: true,
    drawerLabel: 'Home',
    isAuthenticationRequired: true,
  },
  ForgotPassword: {
    screen: 'ForgotPassword',
    showOnDrawer: false,
    drawerLabel: 'Forgot Password',
    isAuthenticationRequired: false,
  },
  Home: {
    screen: 'Home',
    showOnDrawer: false,
    drawerLabel: 'Home',
    isAuthenticationRequired: false,
  },
  Login: {
    screen: 'Login',
    showOnDrawer: true,
    drawerLabel: 'Login',
    isAuthenticationRequired: false,
  },
  Notifications: {
    screen: 'Notifications',
    showOnDrawer: true,
    drawerLabel: 'Notifications',
    isAuthenticationRequired: true,
  },
  Pricing: {
    screen: 'Pricing',
    showOnDrawer: false,
    drawerLabel: 'Pricing',
    isAuthenticationRequired: true,
  },
  IntroSlider: {
    screen: 'IntroSlide',
    showOnDrawer: false,
    drawerLabel: 'Introduction',
    isAuthenticationRequired: false,
  },
  ApplyNow: {
    screen: 'ApplyNow',
    showOnDrawer: false,
    drawerLabel: 'Apply Now',
    isAuthenticationRequired: true,
  },
  ApplySuccessful: {
    screen: 'ApplySuccessful',
    showOnDrawer: false,
    drawerLabel: 'Apply Successful',
    isAuthenticationRequired: true,
  },
  HiddenJobs: {
    screen: 'HiddenJobs',
    showOnDrawer: true,
    drawerLabel: 'Hidden Jobs',
    isAuthenticationRequired: true,
  },
  MyJobs: {
    screen: 'MyJobs',
    showOnDrawer: true,
    drawerLabel: 'My Jobs',
    isAuthenticationRequired: true,
  },
  ShortlistedJobs: {
    screen: 'ShortlistedJobs',
    showOnDrawer: true,
    drawerLabel: 'Shortlisted Jobs',
    isAuthenticationRequired: true,
  },
  Browser: {
    screen: 'Browser',
    showOnDrawer: false,
    drawerLabel: 'Browser',
    isAuthenticationRequired: false,
  },
  Settings: {
    screen: 'Settings',
    showOnDrawer: true,
    drawerLabel: 'Account Settings',
    isAuthenticationRequired: true,
  },
};

//export const ApiBaseUrl = 'https://stage-api.apply4u.co.uk/';
export const ApiBaseUrl = 'https://api.apply4u.co.uk/';
export const TokenApiUrl = 'https://secure.apply4u.co.uk/';

// export const ApiBaseUrl = 'https://stage-api.apply4u.co.uk/';
// export const TokenApiUrl = 'https://stage-secure.apply4u.co.uk/';

//export const ApiBaseUrl = 'http://182.180.143.102:8080/';
//export const ApiBaseUrl = 'https://test-api.apply4u.co.uk/';

export const anyJobTypeId: number = 1;
export const anyJobStatusId: number = 999;
export const minimumSearchRadius: number = 0;
export const maximumSearchRadius: number = 100;
export const milesAwaySliderStep: number = 1;
export const searchDefaultRadius: number = 20;
export const dropdownPlaceholderValue: number = -1;
export const defaultPageSizeSearchResults: number = 10;
export const defaultPageNumberJobSearch: number = 1;
export const logsEnabled: boolean = false;
export const numberOfRecentSearchesToSave = 5;

export const searchWithinDropdownDataSource: IDropDownDataSource[] = [
  {label: 'All', value: 0},
  {label: 'Within 24 hours', value: 1},
  {label: 'Within 48 hours', value: 2},
  {label: 'Within 72 hours', value: 3},
  {label: 'Within 1 week', value: 7},
  {label: 'Within 2 weeks', value: 14},
  {label: 'Within 1 month', value: 30},
  {label: 'Within 3 months', value: 90},
  {label: 'Within 6 months', value: 180},
  {label: 'Within 1 year', value: 365},
];

export const jobCardHeight = 130;
export const storagePermission = {
  title: 'Apply4U',
  message: 'Storage Permission required to choose file',
};

export const locationPermission = {
  title: 'Apply4U',
  message: 'Location Permission required to show you nearby results.',
};

export const zIndex = {
  inputIconZIndex:99010,
  locationAutoComplete : 99020,
  locationAutoCompleteContainer:99020,
}

export const pdfFileExtension = '.pdf';
export const docFileExtension = '.doc';
export const docxFileExtension = '.docx';
export const rtfFileExtension = '.rtf';
export const txtFileExtension = '.txt';

export const defaultInputHeight = 40;
export const defaultInputIconSize = 15;

export const validResumeExtensions = ['.pdf', '.doc', '.docx', '.rtf', '.txt'];
export const alreadyAppliedForJob = 2;
export const apply4UWebUrl = "https://www.apply4u.co.uk";
export const editProfileWebUrl = `${apply4UWebUrl}/users/`;
export const registerNowWebUrl = `${apply4UWebUrl}/account/register`;
export const upgradePackageWebUrl = `${apply4UWebUrl}/our-pricing?jobseeker=true`;
export const blogsWebUrl = `${apply4UWebUrl}/your-job-search`;
export const editSavedSearchWebUrl = (authu:string,authPassword:string,autoSearchId:number) => `${apply4UWebUrl}/direct-login?authu=${authu}&authkey=${authPassword}&redirectURL=/create-auto-searches/${autoSearchId}`;
export const createSavedSearchWebUrl = (authu:string,authPassword:string) => `${apply4UWebUrl}/direct-login?authu=${authu}&authkey=${authPassword}&redirectURL=/create-auto-searches`;
