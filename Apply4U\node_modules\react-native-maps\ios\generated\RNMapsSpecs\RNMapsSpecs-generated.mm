/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNMapsSpecs.h"


@implementation NativeAirMapsModuleSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end

@implementation RCTCxxConvert (NativeAirMapsModule_LatLng)
+ (RCTManagedPointer *)JS_NativeAirMapsModule_LatLng:(id)json
{
  return facebook::react::managedPointer<JS::NativeAirMapsModule::LatLng>(json);
}
@end
@implementation RCTCxxConvert (NativeAirMapsModule_Point)
+ (RCTManagedPointer *)JS_NativeAirMapsModule_Point:(id)json
{
  return facebook::react::managedPointer<JS::NativeAirMapsModule::Point>(json);
}
@end
namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getCamera(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getCamera", @selector(getCamera:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getMarkersFrames(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getMarkersFrames", @selector(getMarkersFrames:onlyVisible:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getMapBoundaries(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getMapBoundaries", @selector(getMapBoundaries:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_takeSnapshot(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "takeSnapshot", @selector(takeSnapshot:config:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getAddressFromCoordinates(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getAddressFromCoordinates", @selector(getAddressFromCoordinates:coordinate:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getPointForCoordinate(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getPointForCoordinate", @selector(getPointForCoordinate:coordinate:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getCoordinateForPoint(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getCoordinateForPoint", @selector(getCoordinateForPoint:point:resolve:reject:), args, count);
    }

  NativeAirMapsModuleSpecJSI::NativeAirMapsModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["getCamera"] = MethodMetadata {1, __hostFunction_NativeAirMapsModuleSpecJSI_getCamera};
        
        
        methodMap_["getMarkersFrames"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_getMarkersFrames};
        
        
        methodMap_["getMapBoundaries"] = MethodMetadata {1, __hostFunction_NativeAirMapsModuleSpecJSI_getMapBoundaries};
        
        
        methodMap_["takeSnapshot"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_takeSnapshot};
        
        
        methodMap_["getAddressFromCoordinates"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_getAddressFromCoordinates};
        setMethodArgConversionSelector(@"getAddressFromCoordinates", 1, @"JS_NativeAirMapsModule_LatLng:");
        
        methodMap_["getPointForCoordinate"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_getPointForCoordinate};
        setMethodArgConversionSelector(@"getPointForCoordinate", 1, @"JS_NativeAirMapsModule_LatLng:");
        
        methodMap_["getCoordinateForPoint"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_getCoordinateForPoint};
        setMethodArgConversionSelector(@"getCoordinateForPoint", 1, @"JS_NativeAirMapsModule_Point:");
  }
} // namespace facebook::react
