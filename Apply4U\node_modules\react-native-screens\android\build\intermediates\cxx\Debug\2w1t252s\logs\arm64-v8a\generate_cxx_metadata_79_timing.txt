# C/C++ build system timings
generate_cxx_metadata
  [gap of 36ms]
  create-invalidation-state 51ms
  [gap of 17ms]
  write-metadata-json-to-file 26ms
generate_cxx_metadata completed in 131ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 39ms
  [gap of 13ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 93ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 36ms
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 82ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 34ms
  [gap of 10ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 79ms

# C/C++ build system timings
generate_cxx_metadata 22ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 31ms]
  create-invalidation-state 51ms
  [gap of 16ms]
  write-metadata-json-to-file 43ms
generate_cxx_metadata completed in 143ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 39ms
  [gap of 17ms]
  write-metadata-json-to-file 35ms
generate_cxx_metadata completed in 113ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 32ms
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 79ms

