/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GenerateComponentHObjCpp.js
*/

#import <Foundation/Foundation.h>
#import <React/RCTDefines.h>
#import <React/RCTLog.h>

NS_ASSUME_NONNULL_BEGIN

@protocol RCTRNMapsGoogleMapViewViewProtocol <NSObject>
- (void)animateToRegion:(NSString *)regionJSON duration:(NSInteger)duration;
- (void)setCamera:(NSString *)cameraJSON;
- (void)animateCamera:(NSString *)cameraJSON duration:(NSInteger)duration;
- (void)fitToElements:(NSString *)edgePaddingJSON animated:(BOOL)animated;
- (void)fitToSuppliedMarkers:(NSString *)markersJSON edgePaddingJSON:(NSString *)edgePaddingJSON animated:(BOOL)animated;
- (void)fitToCoordinates:(NSString *)coordinatesJSON edgePaddingJSON:(NSString *)edgePaddingJSON animated:(BOOL)animated;
- (void)setIndoorActiveLevelIndex:(NSInteger)activeLevelIndex;
@end

RCT_EXTERN inline void RCTRNMapsGoogleMapViewHandleCommand(
  id<RCTRNMapsGoogleMapViewViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"animateToRegion"]) {
#if RCT_DEBUG
  if ([args count] != 2) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsGoogleMapView", commandName, (int)[args count], 2);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsGoogleMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * regionJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSNumber class], @"number", @"RNMapsGoogleMapView", commandName, @"2nd")) {
    return;
  }
#endif
  NSInteger duration = [(NSNumber *)arg1 intValue];

  [componentView animateToRegion:regionJSON duration:duration];
  return;
}

if ([commandName isEqualToString:@"setCamera"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsGoogleMapView", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsGoogleMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * cameraJSON = (NSString *)arg0;

  [componentView setCamera:cameraJSON];
  return;
}

if ([commandName isEqualToString:@"animateCamera"]) {
#if RCT_DEBUG
  if ([args count] != 2) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsGoogleMapView", commandName, (int)[args count], 2);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsGoogleMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * cameraJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSNumber class], @"number", @"RNMapsGoogleMapView", commandName, @"2nd")) {
    return;
  }
#endif
  NSInteger duration = [(NSNumber *)arg1 intValue];

  [componentView animateCamera:cameraJSON duration:duration];
  return;
}

if ([commandName isEqualToString:@"fitToElements"]) {
#if RCT_DEBUG
  if ([args count] != 2) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsGoogleMapView", commandName, (int)[args count], 2);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsGoogleMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * edgePaddingJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSNumber class], @"boolean", @"RNMapsGoogleMapView", commandName, @"2nd")) {
    return;
  }
#endif
  BOOL animated = [(NSNumber *)arg1 boolValue];

  [componentView fitToElements:edgePaddingJSON animated:animated];
  return;
}

if ([commandName isEqualToString:@"fitToSuppliedMarkers"]) {
#if RCT_DEBUG
  if ([args count] != 3) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsGoogleMapView", commandName, (int)[args count], 3);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsGoogleMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * markersJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSString class], @"string", @"RNMapsGoogleMapView", commandName, @"2nd")) {
    return;
  }
#endif
  NSString * edgePaddingJSON = (NSString *)arg1;

NSObject *arg2 = args[2];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg2, [NSNumber class], @"boolean", @"RNMapsGoogleMapView", commandName, @"3rd")) {
    return;
  }
#endif
  BOOL animated = [(NSNumber *)arg2 boolValue];

  [componentView fitToSuppliedMarkers:markersJSON edgePaddingJSON:edgePaddingJSON animated:animated];
  return;
}

if ([commandName isEqualToString:@"fitToCoordinates"]) {
#if RCT_DEBUG
  if ([args count] != 3) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsGoogleMapView", commandName, (int)[args count], 3);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsGoogleMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * coordinatesJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSString class], @"string", @"RNMapsGoogleMapView", commandName, @"2nd")) {
    return;
  }
#endif
  NSString * edgePaddingJSON = (NSString *)arg1;

NSObject *arg2 = args[2];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg2, [NSNumber class], @"boolean", @"RNMapsGoogleMapView", commandName, @"3rd")) {
    return;
  }
#endif
  BOOL animated = [(NSNumber *)arg2 boolValue];

  [componentView fitToCoordinates:coordinatesJSON edgePaddingJSON:edgePaddingJSON animated:animated];
  return;
}

if ([commandName isEqualToString:@"setIndoorActiveLevelIndex"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsGoogleMapView", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"number", @"RNMapsGoogleMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSInteger activeLevelIndex = [(NSNumber *)arg0 intValue];

  [componentView setIndoorActiveLevelIndex:activeLevelIndex];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"RNMapsGoogleMapView", commandName);
#endif
}

@protocol RCTRNMapsGooglePolygonViewProtocol <NSObject>

@end

@protocol RCTRNMapsMapViewViewProtocol <NSObject>
- (void)animateToRegion:(NSString *)regionJSON duration:(NSInteger)duration;
- (void)setCamera:(NSString *)cameraJSON;
- (void)animateCamera:(NSString *)cameraJSON duration:(NSInteger)duration;
- (void)fitToElements:(NSString *)edgePaddingJSON animated:(BOOL)animated;
- (void)fitToSuppliedMarkers:(NSString *)markersJSON edgePaddingJSON:(NSString *)edgePaddingJSON animated:(BOOL)animated;
- (void)fitToCoordinates:(NSString *)coordinatesJSON edgePaddingJSON:(NSString *)edgePaddingJSON animated:(BOOL)animated;
- (void)setIndoorActiveLevelIndex:(NSInteger)activeLevelIndex;
@end

RCT_EXTERN inline void RCTRNMapsMapViewHandleCommand(
  id<RCTRNMapsMapViewViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"animateToRegion"]) {
#if RCT_DEBUG
  if ([args count] != 2) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMapView", commandName, (int)[args count], 2);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * regionJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSNumber class], @"number", @"RNMapsMapView", commandName, @"2nd")) {
    return;
  }
#endif
  NSInteger duration = [(NSNumber *)arg1 intValue];

  [componentView animateToRegion:regionJSON duration:duration];
  return;
}

if ([commandName isEqualToString:@"setCamera"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMapView", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * cameraJSON = (NSString *)arg0;

  [componentView setCamera:cameraJSON];
  return;
}

if ([commandName isEqualToString:@"animateCamera"]) {
#if RCT_DEBUG
  if ([args count] != 2) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMapView", commandName, (int)[args count], 2);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * cameraJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSNumber class], @"number", @"RNMapsMapView", commandName, @"2nd")) {
    return;
  }
#endif
  NSInteger duration = [(NSNumber *)arg1 intValue];

  [componentView animateCamera:cameraJSON duration:duration];
  return;
}

if ([commandName isEqualToString:@"fitToElements"]) {
#if RCT_DEBUG
  if ([args count] != 2) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMapView", commandName, (int)[args count], 2);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * edgePaddingJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSNumber class], @"boolean", @"RNMapsMapView", commandName, @"2nd")) {
    return;
  }
#endif
  BOOL animated = [(NSNumber *)arg1 boolValue];

  [componentView fitToElements:edgePaddingJSON animated:animated];
  return;
}

if ([commandName isEqualToString:@"fitToSuppliedMarkers"]) {
#if RCT_DEBUG
  if ([args count] != 3) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMapView", commandName, (int)[args count], 3);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * markersJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSString class], @"string", @"RNMapsMapView", commandName, @"2nd")) {
    return;
  }
#endif
  NSString * edgePaddingJSON = (NSString *)arg1;

NSObject *arg2 = args[2];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg2, [NSNumber class], @"boolean", @"RNMapsMapView", commandName, @"3rd")) {
    return;
  }
#endif
  BOOL animated = [(NSNumber *)arg2 boolValue];

  [componentView fitToSuppliedMarkers:markersJSON edgePaddingJSON:edgePaddingJSON animated:animated];
  return;
}

if ([commandName isEqualToString:@"fitToCoordinates"]) {
#if RCT_DEBUG
  if ([args count] != 3) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMapView", commandName, (int)[args count], 3);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNMapsMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * coordinatesJSON = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSString class], @"string", @"RNMapsMapView", commandName, @"2nd")) {
    return;
  }
#endif
  NSString * edgePaddingJSON = (NSString *)arg1;

NSObject *arg2 = args[2];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg2, [NSNumber class], @"boolean", @"RNMapsMapView", commandName, @"3rd")) {
    return;
  }
#endif
  BOOL animated = [(NSNumber *)arg2 boolValue];

  [componentView fitToCoordinates:coordinatesJSON edgePaddingJSON:edgePaddingJSON animated:animated];
  return;
}

if ([commandName isEqualToString:@"setIndoorActiveLevelIndex"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMapView", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"number", @"RNMapsMapView", commandName, @"1st")) {
    return;
  }
#endif
  NSInteger activeLevelIndex = [(NSNumber *)arg0 intValue];

  [componentView setIndoorActiveLevelIndex:activeLevelIndex];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"RNMapsMapView", commandName);
#endif
}

@protocol RCTRNMapsMarkerViewProtocol <NSObject>
- (void)animateToCoordinates:(double)latitude longitude:(double)longitude duration:(NSInteger)duration;
- (void)setCoordinates:(double)latitude longitude:(double)longitude;
- (void)showCallout;
- (void)hideCallout;
- (void)redrawCallout;
- (void)redraw;
@end

RCT_EXTERN inline void RCTRNMapsMarkerHandleCommand(
  id<RCTRNMapsMarkerViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"animateToCoordinates"]) {
#if RCT_DEBUG
  if ([args count] != 3) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMarker", commandName, (int)[args count], 3);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"double", @"RNMapsMarker", commandName, @"1st")) {
    return;
  }
#endif
  double latitude = [(NSNumber *)arg0 doubleValue];

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSNumber class], @"double", @"RNMapsMarker", commandName, @"2nd")) {
    return;
  }
#endif
  double longitude = [(NSNumber *)arg1 doubleValue];

NSObject *arg2 = args[2];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg2, [NSNumber class], @"number", @"RNMapsMarker", commandName, @"3rd")) {
    return;
  }
#endif
  NSInteger duration = [(NSNumber *)arg2 intValue];

  [componentView animateToCoordinates:latitude longitude:longitude duration:duration];
  return;
}

if ([commandName isEqualToString:@"setCoordinates"]) {
#if RCT_DEBUG
  if ([args count] != 2) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMarker", commandName, (int)[args count], 2);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"double", @"RNMapsMarker", commandName, @"1st")) {
    return;
  }
#endif
  double latitude = [(NSNumber *)arg0 doubleValue];

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSNumber class], @"double", @"RNMapsMarker", commandName, @"2nd")) {
    return;
  }
#endif
  double longitude = [(NSNumber *)arg1 doubleValue];

  [componentView setCoordinates:latitude longitude:longitude];
  return;
}

if ([commandName isEqualToString:@"showCallout"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMarker", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView showCallout];
  return;
}

if ([commandName isEqualToString:@"hideCallout"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMarker", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView hideCallout];
  return;
}

if ([commandName isEqualToString:@"redrawCallout"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMarker", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView redrawCallout];
  return;
}

if ([commandName isEqualToString:@"redraw"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNMapsMarker", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView redraw];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"RNMapsMarker", commandName);
#endif
}

NS_ASSUME_NONNULL_END