# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 42ms
  [gap of 17ms]
generate_cxx_metadata completed in 87ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 29ms
  [gap of 11ms]
generate_cxx_metadata completed in 56ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 29ms
  [gap of 11ms]
generate_cxx_metadata completed in 54ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 27ms
  [gap of 11ms]
generate_cxx_metadata completed in 52ms

# C/C++ build system timings
generate_cxx_metadata 24ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 34ms
  [gap of 12ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 84ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 40ms
  [gap of 11ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 88ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 30ms
  [gap of 10ms]
generate_cxx_metadata completed in 54ms

