# C/C++ build system timings
generate_cxx_metadata 21ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 45ms
  [gap of 14ms]
  write-metadata-json-to-file 25ms
generate_cxx_metadata completed in 113ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 11ms
  [gap of 20ms]
generate_cxx_metadata completed in 33ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 33ms
  [gap of 12ms]
generate_cxx_metadata completed in 62ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 28ms
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 67ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 37ms
  [gap of 14ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 91ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 11ms
  [gap of 21ms]
generate_cxx_metadata completed in 35ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 34ms
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 77ms

