# C/C++ build system timings
generate_cxx_metadata
  [gap of 79ms]
  create-invalidation-state 106ms
  [gap of 37ms]
  write-metadata-json-to-file 40ms
generate_cxx_metadata completed in 262ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 51ms]
  create-invalidation-state 124ms
  [gap of 36ms]
  write-metadata-json-to-file 44ms
generate_cxx_metadata completed in 256ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 78ms
  [gap of 39ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 148ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 52ms]
  create-invalidation-state 132ms
  [gap of 49ms]
  write-metadata-json-to-file 32ms
generate_cxx_metadata completed in 265ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 49ms]
  create-invalidation-state 100ms
  [gap of 32ms]
  write-metadata-json-to-file 37ms
generate_cxx_metadata completed in 219ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 60ms]
  create-invalidation-state 80ms
  [gap of 37ms]
  write-metadata-json-to-file 41ms
generate_cxx_metadata completed in 220ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 73ms]
  create-invalidation-state 100ms
  [gap of 36ms]
  write-metadata-json-to-file 37ms
generate_cxx_metadata completed in 247ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 74ms]
  create-invalidation-state 119ms
  [gap of 33ms]
  write-metadata-json-to-file 40ms
generate_cxx_metadata completed in 266ms

