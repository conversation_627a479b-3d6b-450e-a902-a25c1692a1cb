import React from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  ImageBackground,
  SafeAreaView,
} from 'react-native';

import {transparentLogo, background_1} from '../../../assets';
import {useTranslation} from 'react-i18next';
import {ITheme, useCurrentTheme} from '../../../theme';
import {Button, Text, InputText} from '../../common';
import {InputType} from '../../../enum';
import {ValidationResults} from '../../../interfaces/validation';
import {ILogin} from '../../../interfaces';

interface IProps {
  loginData: ILogin;
  validationResults?: ValidationResults<ILogin>;
  setLoginData: (data: ILogin) => void;
  login: () => void;
  registerNow: () => void;
  resetValidation?: (key: keyof ILogin) => void;
  validate?: (key: keyof ILogin) => void;
  forgotPasswordClick:() => void;
  goToMap: () => void;
}

export const LoginView = (props: IProps) => {
  const {t} = useTranslation(['Login']);
  const styles = useCurrentTheme(createStyles);

  interface IKeys {
    email: keyof ILogin;
    password: keyof ILogin;
  }
  const keys: IKeys = {
    email: 'email',
    password: 'password',
  };

  const handleChange = (value: string, key: string) => {
    let updatedModel: ILogin = {...props.loginData};
    if (key in updatedModel) {
      updatedModel[key] = value;
      props.setLoginData({...updatedModel});
    }
  };

  return (
    <ImageBackground source={background_1} style={styles.container}>
      <SafeAreaView>
        <ScrollView
          contentInsetAdjustmentBehavior="automatic"
          keyboardShouldPersistTaps="always"
          keyboardDismissMode="interactive">
          <View style={styles.logoContainer}>
            <Image
              source={transparentLogo}
              style={styles.appLogo}
              resizeMode="contain"
            />
          </View>

          <View style={styles.loginBox}>
            <KeyboardAvoidingView enabled>
              <InputText
                name={keys.email.toString()}
                inputType={InputType.EmailAddress}
                onChangeText={handleChange}
                placeholder={t('Placeholders.Enter_Email')}
                value={props.loginData ? props.loginData.email : ''}
                validationResults={
                  props.validationResults?.email?.validationResults
                }
                onBlur={e => {
                  if (props.validate) {
                    props.validate(keys.email);
                  }
                }}
                onFocus={e => {
                  if (props.resetValidation) {
                    props.resetValidation(keys.email);
                  }
                }}
              />

              <InputText
                name={keys.password.toString()}
                inputType={InputType.Password}
                onChangeText={handleChange}
                placeholder={t('Placeholders.Enter_Password')}
                value={props.loginData ? props.loginData.password : ''}
                validationResults={
                  props.validationResults?.password?.validationResults
                }
                onBlur={e => {
                  if (props.validate) {
                    props.validate(keys.password);
                  }
                }}
                onFocus={e => {
                  if (props.resetValidation) {
                    props.resetValidation(keys.password);
                  }
                }}
              />

              <Text
                pressed={() => {
                  Keyboard.dismiss();
                  props.forgotPasswordClick();
                }}
                styles={styles.forgotTextStyle}
                text={t('Labels.Forgot_Password')}
              />

              <Button
                pressed={() => {
                  Keyboard.dismiss();
                  props.login();
                }}
                loadingText={t('Messages.Logging_In')}
                text={t('Buttons.Login')}
              />

              <Button
                pressed={() => {
                  Keyboard.dismiss();
                  props.goToMap();
                }}
                styles={styles.mapButton}
                text="Go to Map"
              />
            </KeyboardAvoidingView>
          </View>

          {/* <Text styles={styles.textStyle} text={t('Labels.Register_With')} />

          <View style={styles.socialIconsStyle}>
            <Image source={facebookLogo} style={styles.facebook} />
            <Image source={twitterLogo} style={styles.twitter} />
            <Image source={googlePlusLogo} style={styles.googlePlus} />
          </View> */}

          <View style={styles.registerView}>
            <Text
              styles={styles.textStyle}
              text={t('Labels.Not_A_Member_Yet')}
            />
            <Button
              styles={styles.registerNowButton}
              text={t('Buttons.Register_Now')}
              pressed={() => {
                props.registerNow();
              }}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'column',
      flexGrow: 1,
      justifyContent: 'space-between',
    },
    logoContainer: {
      alignItems: 'center',
    },
    appLogo: {
      height: 100,
      width: '50%',
      marginTop: theme.spacing(30),
      marginRight: theme.spacing(20),
      marginBottom: theme.spacing(20),
      marginLeft: theme.spacing(20),
      resizeMode: 'contain',
    },
    loginBox: {
      flex: 1,
      margin: theme.spacing(10),
      padding: theme.spacing(7.5),
      height: '100%',
      borderRadius: theme.borderRadius(5),
      backgroundColor: theme.backgroundPalette.primary,
    },
    inputView: {
      height: 40,
      marginBottom: theme.spacing(7.5),
      flexDirection: 'row',
    },
    registerNowButton: {
      ...theme.buttons.secondary,
      marginRight: theme.spacing(10),
      marginBottom: theme.spacing(10),
      marginLeft: theme.spacing(10),
    },
    mapButton: {
      ...theme.buttons.secondary,
      marginTop: theme.spacing(5),
      backgroundColor: theme.palette.secondary,
    },
    inputStyle: {
      flex: 1,
      borderWidth: 1,
      color: theme.palette.gray,
      paddingLeft: theme.spacing(7.5),
      paddingRight: theme.spacing(7.5),
      borderRadius: theme.borderRadius(15),
      borderColor: theme.palette.lightGray,
      backgroundColor: theme.palette.white,
    },
    textStyle: {
      padding: theme.spacing(5),
      ...theme.typography.bold.small,
      color: theme.palette.white,
      textAlign: 'center',
      alignSelf: 'center',
    },
    forgotTextStyle: {
      marginRight: theme.spacing(5),
      marginTop: theme.spacing(5),
      marginBottom: theme.spacing(5),
      textAlign: 'right',
      ...theme.typography.bold.small,
      color: theme.palette.primary,
    },
    socialIconsStyle: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    registerView: {
      flex: 1,
      marginTop: theme.spacing(50),
      justifyContent: 'flex-end',
    },
    facebook: {
      height: 60,
      width: '10%',
      marginRight: theme.spacing(5),
      resizeMode: 'contain',
    },
    twitter: {
      height: 60,
      width: '10%',
      marginRight: theme.spacing(5),
      resizeMode: 'contain',
    },
    googlePlus: {
      height: 60,
      width: '10%',
      resizeMode: 'contain',
    },
  });

  return {...styles};
};
