# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 52ms
  [gap of 21ms]
generate_cxx_metadata completed in 96ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 29ms
  [gap of 11ms]
generate_cxx_metadata completed in 58ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 31ms
  [gap of 12ms]
generate_cxx_metadata completed in 59ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 25ms
  [gap of 11ms]
generate_cxx_metadata completed in 50ms

# C/C++ build system timings
generate_cxx_metadata 21ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 33ms
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 76ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 40ms
  [gap of 11ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 96ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 48ms
  [gap of 17ms]
  write-metadata-json-to-file 24ms
generate_cxx_metadata completed in 113ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 31ms
  [gap of 12ms]
generate_cxx_metadata completed in 61ms

