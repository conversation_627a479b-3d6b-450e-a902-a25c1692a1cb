/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class RNMapsUrlTileManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMapsUrlTileManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNMapsUrlTileManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "doubleTileSize":
        mViewManager.setDoubleTileSize(view, value == null ? false : (boolean) value);
        break;
      case "flipY":
        mViewManager.setFlipY(view, value == null ? false : (boolean) value);
        break;
      case "maximumNativeZ":
        mViewManager.setMaximumNativeZ(view, value == null ? 100 : ((Double) value).intValue());
        break;
      case "maximumZ":
        mViewManager.setMaximumZ(view, value == null ? 100 : ((Double) value).intValue());
        break;
      case "minimumZ":
        mViewManager.setMinimumZ(view, value == null ? 0 : ((Double) value).intValue());
        break;
      case "offlineMode":
        mViewManager.setOfflineMode(view, value == null ? false : (boolean) value);
        break;
      case "shouldReplaceMapContent":
        mViewManager.setShouldReplaceMapContent(view, value == null ? false : (boolean) value);
        break;
      case "tileCacheMaxAge":
        mViewManager.setTileCacheMaxAge(view, value == null ? 0 : ((Double) value).intValue());
        break;
      case "tileCachePath":
        mViewManager.setTileCachePath(view, value == null ? null : (String) value);
        break;
      case "tileSize":
        mViewManager.setTileSize(view, value == null ? 256 : ((Double) value).intValue());
        break;
      case "urlTemplate":
        mViewManager.setUrlTemplate(view, value == null ? null : (String) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
