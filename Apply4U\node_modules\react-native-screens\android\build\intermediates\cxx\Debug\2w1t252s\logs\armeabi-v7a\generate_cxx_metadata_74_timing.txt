# C/C++ build system timings
generate_cxx_metadata 25ms

# C/C++ build system timings
generate_cxx_metadata 28ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 32ms
  [gap of 13ms]
generate_cxx_metadata completed in 65ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 37ms
  [gap of 12ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 88ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
  [gap of 25ms]
generate_cxx_metadata completed in 42ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 34ms
  [gap of 10ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 77ms

