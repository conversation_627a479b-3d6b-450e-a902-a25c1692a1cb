
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>
#include <react/renderer/graphics/Color.h>
#include <react/renderer/imagemanager/primitives.h>
#include <vector>

namespace facebook::react {

class RNMapsCalloutProps final : public ViewProps {
 public:
  RNMapsCalloutProps() = default;
  RNMapsCalloutProps(const PropsParserContext& context, const RNMapsCalloutProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool alphaHitTest{false};
  bool tooltip{false};
};

struct RNMapsCircleCenterStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsCircleCenterStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsCircleCenterStruct &value) {
  return "[Object RNMapsCircleCenterStruct]";
}
class RNMapsCircleProps final : public ViewProps {
 public:
  RNMapsCircleProps() = default;
  RNMapsCircleProps(const PropsParserContext& context, const RNMapsCircleProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  RNMapsCircleCenterStruct center{};
  SharedColor fillColor{};
  double radius{0.0};
  SharedColor strokeColor{};
  Float strokeWidth{0.0};
  bool tappable{false};
};

enum class RNMapsGoogleMapViewMapType { Hybrid, MutedStandard, None, Satellite, Standard, Terrain, SatelliteFlyover, HybridFlyover };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewMapType &result) {
  auto string = (std::string)value;
  if (string == "hybrid") { result = RNMapsGoogleMapViewMapType::Hybrid; return; }
  if (string == "mutedStandard") { result = RNMapsGoogleMapViewMapType::MutedStandard; return; }
  if (string == "none") { result = RNMapsGoogleMapViewMapType::None; return; }
  if (string == "satellite") { result = RNMapsGoogleMapViewMapType::Satellite; return; }
  if (string == "standard") { result = RNMapsGoogleMapViewMapType::Standard; return; }
  if (string == "terrain") { result = RNMapsGoogleMapViewMapType::Terrain; return; }
  if (string == "satelliteFlyover") { result = RNMapsGoogleMapViewMapType::SatelliteFlyover; return; }
  if (string == "hybridFlyover") { result = RNMapsGoogleMapViewMapType::HybridFlyover; return; }
  abort();
}

static inline std::string toString(const RNMapsGoogleMapViewMapType &value) {
  switch (value) {
    case RNMapsGoogleMapViewMapType::Hybrid: return "hybrid";
    case RNMapsGoogleMapViewMapType::MutedStandard: return "mutedStandard";
    case RNMapsGoogleMapViewMapType::None: return "none";
    case RNMapsGoogleMapViewMapType::Satellite: return "satellite";
    case RNMapsGoogleMapViewMapType::Standard: return "standard";
    case RNMapsGoogleMapViewMapType::Terrain: return "terrain";
    case RNMapsGoogleMapViewMapType::SatelliteFlyover: return "satelliteFlyover";
    case RNMapsGoogleMapViewMapType::HybridFlyover: return "hybridFlyover";
  }
}
enum class RNMapsGoogleMapViewPaddingAdjustmentBehavior { Always, Automatic, Never };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewPaddingAdjustmentBehavior &result) {
  auto string = (std::string)value;
  if (string == "always") { result = RNMapsGoogleMapViewPaddingAdjustmentBehavior::Always; return; }
  if (string == "automatic") { result = RNMapsGoogleMapViewPaddingAdjustmentBehavior::Automatic; return; }
  if (string == "never") { result = RNMapsGoogleMapViewPaddingAdjustmentBehavior::Never; return; }
  abort();
}

static inline std::string toString(const RNMapsGoogleMapViewPaddingAdjustmentBehavior &value) {
  switch (value) {
    case RNMapsGoogleMapViewPaddingAdjustmentBehavior::Always: return "always";
    case RNMapsGoogleMapViewPaddingAdjustmentBehavior::Automatic: return "automatic";
    case RNMapsGoogleMapViewPaddingAdjustmentBehavior::Never: return "never";
  }
}
enum class RNMapsGoogleMapViewUserInterfaceStyle { System, Light, Dark };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewUserInterfaceStyle &result) {
  auto string = (std::string)value;
  if (string == "system") { result = RNMapsGoogleMapViewUserInterfaceStyle::System; return; }
  if (string == "light") { result = RNMapsGoogleMapViewUserInterfaceStyle::Light; return; }
  if (string == "dark") { result = RNMapsGoogleMapViewUserInterfaceStyle::Dark; return; }
  abort();
}

static inline std::string toString(const RNMapsGoogleMapViewUserInterfaceStyle &value) {
  switch (value) {
    case RNMapsGoogleMapViewUserInterfaceStyle::System: return "system";
    case RNMapsGoogleMapViewUserInterfaceStyle::Light: return "light";
    case RNMapsGoogleMapViewUserInterfaceStyle::Dark: return "dark";
  }
}
enum class RNMapsGoogleMapViewUserLocationPriority { Balanced, High, Low, Passive };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewUserLocationPriority &result) {
  auto string = (std::string)value;
  if (string == "balanced") { result = RNMapsGoogleMapViewUserLocationPriority::Balanced; return; }
  if (string == "high") { result = RNMapsGoogleMapViewUserLocationPriority::High; return; }
  if (string == "low") { result = RNMapsGoogleMapViewUserLocationPriority::Low; return; }
  if (string == "passive") { result = RNMapsGoogleMapViewUserLocationPriority::Passive; return; }
  abort();
}

static inline std::string toString(const RNMapsGoogleMapViewUserLocationPriority &value) {
  switch (value) {
    case RNMapsGoogleMapViewUserLocationPriority::Balanced: return "balanced";
    case RNMapsGoogleMapViewUserLocationPriority::High: return "high";
    case RNMapsGoogleMapViewUserLocationPriority::Low: return "low";
    case RNMapsGoogleMapViewUserLocationPriority::Passive: return "passive";
  }
}
struct RNMapsGoogleMapViewCameraCenterStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewCameraCenterStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsGoogleMapViewCameraCenterStruct &value) {
  return "[Object RNMapsGoogleMapViewCameraCenterStruct]";
}

struct RNMapsGoogleMapViewCameraStruct {
  double altitude{0.0};
  RNMapsGoogleMapViewCameraCenterStruct center{};
  double heading{0.0};
  double pitch{0.0};
  Float zoom{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewCameraStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_altitude = map.find("altitude");
  if (tmp_altitude != map.end()) {
    fromRawValue(context, tmp_altitude->second, result.altitude);
  }
  auto tmp_center = map.find("center");
  if (tmp_center != map.end()) {
    fromRawValue(context, tmp_center->second, result.center);
  }
  auto tmp_heading = map.find("heading");
  if (tmp_heading != map.end()) {
    fromRawValue(context, tmp_heading->second, result.heading);
  }
  auto tmp_pitch = map.find("pitch");
  if (tmp_pitch != map.end()) {
    fromRawValue(context, tmp_pitch->second, result.pitch);
  }
  auto tmp_zoom = map.find("zoom");
  if (tmp_zoom != map.end()) {
    fromRawValue(context, tmp_zoom->second, result.zoom);
  }
}

static inline std::string toString(const RNMapsGoogleMapViewCameraStruct &value) {
  return "[Object RNMapsGoogleMapViewCameraStruct]";
}

struct RNMapsGoogleMapViewInitialCameraCenterStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewInitialCameraCenterStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsGoogleMapViewInitialCameraCenterStruct &value) {
  return "[Object RNMapsGoogleMapViewInitialCameraCenterStruct]";
}

struct RNMapsGoogleMapViewInitialCameraStruct {
  double altitude{0.0};
  RNMapsGoogleMapViewInitialCameraCenterStruct center{};
  double heading{0.0};
  double pitch{0.0};
  Float zoom{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewInitialCameraStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_altitude = map.find("altitude");
  if (tmp_altitude != map.end()) {
    fromRawValue(context, tmp_altitude->second, result.altitude);
  }
  auto tmp_center = map.find("center");
  if (tmp_center != map.end()) {
    fromRawValue(context, tmp_center->second, result.center);
  }
  auto tmp_heading = map.find("heading");
  if (tmp_heading != map.end()) {
    fromRawValue(context, tmp_heading->second, result.heading);
  }
  auto tmp_pitch = map.find("pitch");
  if (tmp_pitch != map.end()) {
    fromRawValue(context, tmp_pitch->second, result.pitch);
  }
  auto tmp_zoom = map.find("zoom");
  if (tmp_zoom != map.end()) {
    fromRawValue(context, tmp_zoom->second, result.zoom);
  }
}

static inline std::string toString(const RNMapsGoogleMapViewInitialCameraStruct &value) {
  return "[Object RNMapsGoogleMapViewInitialCameraStruct]";
}

struct RNMapsGoogleMapViewInitialRegionStruct {
  double latitude{0.0};
  double longitude{0.0};
  double latitudeDelta{0.0};
  double longitudeDelta{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewInitialRegionStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
  auto tmp_latitudeDelta = map.find("latitudeDelta");
  if (tmp_latitudeDelta != map.end()) {
    fromRawValue(context, tmp_latitudeDelta->second, result.latitudeDelta);
  }
  auto tmp_longitudeDelta = map.find("longitudeDelta");
  if (tmp_longitudeDelta != map.end()) {
    fromRawValue(context, tmp_longitudeDelta->second, result.longitudeDelta);
  }
}

static inline std::string toString(const RNMapsGoogleMapViewInitialRegionStruct &value) {
  return "[Object RNMapsGoogleMapViewInitialRegionStruct]";
}

struct RNMapsGoogleMapViewMapPaddingStruct {
  double top{0.0};
  double right{0.0};
  double bottom{0.0};
  double left{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewMapPaddingStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_top = map.find("top");
  if (tmp_top != map.end()) {
    fromRawValue(context, tmp_top->second, result.top);
  }
  auto tmp_right = map.find("right");
  if (tmp_right != map.end()) {
    fromRawValue(context, tmp_right->second, result.right);
  }
  auto tmp_bottom = map.find("bottom");
  if (tmp_bottom != map.end()) {
    fromRawValue(context, tmp_bottom->second, result.bottom);
  }
  auto tmp_left = map.find("left");
  if (tmp_left != map.end()) {
    fromRawValue(context, tmp_left->second, result.left);
  }
}

static inline std::string toString(const RNMapsGoogleMapViewMapPaddingStruct &value) {
  return "[Object RNMapsGoogleMapViewMapPaddingStruct]";
}

struct RNMapsGoogleMapViewRegionStruct {
  double latitude{0.0};
  double longitude{0.0};
  double latitudeDelta{0.0};
  double longitudeDelta{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGoogleMapViewRegionStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
  auto tmp_latitudeDelta = map.find("latitudeDelta");
  if (tmp_latitudeDelta != map.end()) {
    fromRawValue(context, tmp_latitudeDelta->second, result.latitudeDelta);
  }
  auto tmp_longitudeDelta = map.find("longitudeDelta");
  if (tmp_longitudeDelta != map.end()) {
    fromRawValue(context, tmp_longitudeDelta->second, result.longitudeDelta);
  }
}

static inline std::string toString(const RNMapsGoogleMapViewRegionStruct &value) {
  return "[Object RNMapsGoogleMapViewRegionStruct]";
}
class RNMapsGoogleMapViewProps final : public ViewProps {
 public:
  RNMapsGoogleMapViewProps() = default;
  RNMapsGoogleMapViewProps(const PropsParserContext& context, const RNMapsGoogleMapViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  RNMapsGoogleMapViewCameraStruct camera{};
  RNMapsGoogleMapViewInitialCameraStruct initialCamera{};
  RNMapsGoogleMapViewInitialRegionStruct initialRegion{};
  std::string kmlSrc{};
  std::string googleMapId{};
  SharedColor loadingBackgroundColor{};
  RNMapsGoogleMapViewMapPaddingStruct mapPadding{};
  RNMapsGoogleMapViewMapType mapType{RNMapsGoogleMapViewMapType::Standard};
  Float maxZoom{0.0};
  Float minZoom{0.0};
  RNMapsGoogleMapViewPaddingAdjustmentBehavior paddingAdjustmentBehavior{RNMapsGoogleMapViewPaddingAdjustmentBehavior::Never};
  bool pitchEnabled{true};
  RNMapsGoogleMapViewRegionStruct region{};
  bool rotateEnabled{true};
  bool scrollDuringRotateOrZoomEnabled{true};
  bool scrollEnabled{true};
  bool showsBuildings{true};
  bool showsCompass{true};
  bool showsIndoorLevelPicker{false};
  bool showsIndoors{true};
  bool showsMyLocationButton{true};
  bool showsPointsOfInterest{false};
  bool showsScale{false};
  bool showsTraffic{false};
  bool showsUserLocation{false};
  RNMapsGoogleMapViewUserInterfaceStyle userInterfaceStyle{RNMapsGoogleMapViewUserInterfaceStyle::System};
  std::string customMapStyleString{};
  bool userLocationCalloutEnabled{false};
  int userLocationFastestInterval{0};
  RNMapsGoogleMapViewUserLocationPriority userLocationPriority{RNMapsGoogleMapViewUserLocationPriority::High};
  int userLocationUpdateInterval{0};
  bool zoomControlEnabled{false};
  bool zoomEnabled{true};
  bool zoomTapEnabled{true};
};

struct RNMapsGooglePolygonCoordinatesStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGooglePolygonCoordinatesStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsGooglePolygonCoordinatesStruct &value) {
  return "[Object RNMapsGooglePolygonCoordinatesStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<RNMapsGooglePolygonCoordinatesStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    RNMapsGooglePolygonCoordinatesStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}


struct RNMapsGooglePolygonHolesStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsGooglePolygonHolesStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsGooglePolygonHolesStruct &value) {
  return "[Object RNMapsGooglePolygonHolesStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<std::vector<RNMapsGooglePolygonHolesStruct>> &result) {
  auto items = (std::vector<std::vector<RawValue>>)value;
  for (const std::vector<RawValue> &item : items) {
    auto nestedArray = std::vector<RNMapsGooglePolygonHolesStruct>{};
    for (const RawValue &nestedItem : item) {
      RNMapsGooglePolygonHolesStruct newItem;
      fromRawValue(context, nestedItem, newItem);
      nestedArray.emplace_back(newItem);
    }
    result.emplace_back(nestedArray);
  }
}

class RNMapsGooglePolygonProps final : public ViewProps {
 public:
  RNMapsGooglePolygonProps() = default;
  RNMapsGooglePolygonProps(const PropsParserContext& context, const RNMapsGooglePolygonProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::vector<RNMapsGooglePolygonCoordinatesStruct> coordinates{};
  SharedColor fillColor{};
  SharedColor strokeColor{};
  bool geodesic{false};
  std::vector<std::vector<RNMapsGooglePolygonHolesStruct>> holes{};
  bool tappable{false};
};

enum class RNMapsMapViewGoogleRenderer { LATEST, LEGACY };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewGoogleRenderer &result) {
  auto string = (std::string)value;
  if (string == "LATEST") { result = RNMapsMapViewGoogleRenderer::LATEST; return; }
  if (string == "LEGACY") { result = RNMapsMapViewGoogleRenderer::LEGACY; return; }
  abort();
}

static inline std::string toString(const RNMapsMapViewGoogleRenderer &value) {
  switch (value) {
    case RNMapsMapViewGoogleRenderer::LATEST: return "LATEST";
    case RNMapsMapViewGoogleRenderer::LEGACY: return "LEGACY";
  }
}
enum class RNMapsMapViewMapType { Hybrid, MutedStandard, None, Satellite, Standard, Terrain, SatelliteFlyover, HybridFlyover };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewMapType &result) {
  auto string = (std::string)value;
  if (string == "hybrid") { result = RNMapsMapViewMapType::Hybrid; return; }
  if (string == "mutedStandard") { result = RNMapsMapViewMapType::MutedStandard; return; }
  if (string == "none") { result = RNMapsMapViewMapType::None; return; }
  if (string == "satellite") { result = RNMapsMapViewMapType::Satellite; return; }
  if (string == "standard") { result = RNMapsMapViewMapType::Standard; return; }
  if (string == "terrain") { result = RNMapsMapViewMapType::Terrain; return; }
  if (string == "satelliteFlyover") { result = RNMapsMapViewMapType::SatelliteFlyover; return; }
  if (string == "hybridFlyover") { result = RNMapsMapViewMapType::HybridFlyover; return; }
  abort();
}

static inline std::string toString(const RNMapsMapViewMapType &value) {
  switch (value) {
    case RNMapsMapViewMapType::Hybrid: return "hybrid";
    case RNMapsMapViewMapType::MutedStandard: return "mutedStandard";
    case RNMapsMapViewMapType::None: return "none";
    case RNMapsMapViewMapType::Satellite: return "satellite";
    case RNMapsMapViewMapType::Standard: return "standard";
    case RNMapsMapViewMapType::Terrain: return "terrain";
    case RNMapsMapViewMapType::SatelliteFlyover: return "satelliteFlyover";
    case RNMapsMapViewMapType::HybridFlyover: return "hybridFlyover";
  }
}
enum class RNMapsMapViewPaddingAdjustmentBehavior { Always, Automatic, Never };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewPaddingAdjustmentBehavior &result) {
  auto string = (std::string)value;
  if (string == "always") { result = RNMapsMapViewPaddingAdjustmentBehavior::Always; return; }
  if (string == "automatic") { result = RNMapsMapViewPaddingAdjustmentBehavior::Automatic; return; }
  if (string == "never") { result = RNMapsMapViewPaddingAdjustmentBehavior::Never; return; }
  abort();
}

static inline std::string toString(const RNMapsMapViewPaddingAdjustmentBehavior &value) {
  switch (value) {
    case RNMapsMapViewPaddingAdjustmentBehavior::Always: return "always";
    case RNMapsMapViewPaddingAdjustmentBehavior::Automatic: return "automatic";
    case RNMapsMapViewPaddingAdjustmentBehavior::Never: return "never";
  }
}
enum class RNMapsMapViewUserInterfaceStyle { System, Light, Dark };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewUserInterfaceStyle &result) {
  auto string = (std::string)value;
  if (string == "system") { result = RNMapsMapViewUserInterfaceStyle::System; return; }
  if (string == "light") { result = RNMapsMapViewUserInterfaceStyle::Light; return; }
  if (string == "dark") { result = RNMapsMapViewUserInterfaceStyle::Dark; return; }
  abort();
}

static inline std::string toString(const RNMapsMapViewUserInterfaceStyle &value) {
  switch (value) {
    case RNMapsMapViewUserInterfaceStyle::System: return "system";
    case RNMapsMapViewUserInterfaceStyle::Light: return "light";
    case RNMapsMapViewUserInterfaceStyle::Dark: return "dark";
  }
}
enum class RNMapsMapViewUserLocationPriority { Balanced, High, Low, Passive };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewUserLocationPriority &result) {
  auto string = (std::string)value;
  if (string == "balanced") { result = RNMapsMapViewUserLocationPriority::Balanced; return; }
  if (string == "high") { result = RNMapsMapViewUserLocationPriority::High; return; }
  if (string == "low") { result = RNMapsMapViewUserLocationPriority::Low; return; }
  if (string == "passive") { result = RNMapsMapViewUserLocationPriority::Passive; return; }
  abort();
}

static inline std::string toString(const RNMapsMapViewUserLocationPriority &value) {
  switch (value) {
    case RNMapsMapViewUserLocationPriority::Balanced: return "balanced";
    case RNMapsMapViewUserLocationPriority::High: return "high";
    case RNMapsMapViewUserLocationPriority::Low: return "low";
    case RNMapsMapViewUserLocationPriority::Passive: return "passive";
  }
}
struct RNMapsMapViewCameraCenterStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewCameraCenterStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsMapViewCameraCenterStruct &value) {
  return "[Object RNMapsMapViewCameraCenterStruct]";
}

struct RNMapsMapViewCameraStruct {
  double altitude{0.0};
  RNMapsMapViewCameraCenterStruct center{};
  double heading{0.0};
  double pitch{0.0};
  Float zoom{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewCameraStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_altitude = map.find("altitude");
  if (tmp_altitude != map.end()) {
    fromRawValue(context, tmp_altitude->second, result.altitude);
  }
  auto tmp_center = map.find("center");
  if (tmp_center != map.end()) {
    fromRawValue(context, tmp_center->second, result.center);
  }
  auto tmp_heading = map.find("heading");
  if (tmp_heading != map.end()) {
    fromRawValue(context, tmp_heading->second, result.heading);
  }
  auto tmp_pitch = map.find("pitch");
  if (tmp_pitch != map.end()) {
    fromRawValue(context, tmp_pitch->second, result.pitch);
  }
  auto tmp_zoom = map.find("zoom");
  if (tmp_zoom != map.end()) {
    fromRawValue(context, tmp_zoom->second, result.zoom);
  }
}

static inline std::string toString(const RNMapsMapViewCameraStruct &value) {
  return "[Object RNMapsMapViewCameraStruct]";
}

struct RNMapsMapViewCompassOffsetStruct {
  double x{0.0};
  double y{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewCompassOffsetStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_x = map.find("x");
  if (tmp_x != map.end()) {
    fromRawValue(context, tmp_x->second, result.x);
  }
  auto tmp_y = map.find("y");
  if (tmp_y != map.end()) {
    fromRawValue(context, tmp_y->second, result.y);
  }
}

static inline std::string toString(const RNMapsMapViewCompassOffsetStruct &value) {
  return "[Object RNMapsMapViewCompassOffsetStruct]";
}

struct RNMapsMapViewInitialCameraCenterStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewInitialCameraCenterStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsMapViewInitialCameraCenterStruct &value) {
  return "[Object RNMapsMapViewInitialCameraCenterStruct]";
}

struct RNMapsMapViewInitialCameraStruct {
  double altitude{0.0};
  RNMapsMapViewInitialCameraCenterStruct center{};
  double heading{0.0};
  double pitch{0.0};
  Float zoom{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewInitialCameraStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_altitude = map.find("altitude");
  if (tmp_altitude != map.end()) {
    fromRawValue(context, tmp_altitude->second, result.altitude);
  }
  auto tmp_center = map.find("center");
  if (tmp_center != map.end()) {
    fromRawValue(context, tmp_center->second, result.center);
  }
  auto tmp_heading = map.find("heading");
  if (tmp_heading != map.end()) {
    fromRawValue(context, tmp_heading->second, result.heading);
  }
  auto tmp_pitch = map.find("pitch");
  if (tmp_pitch != map.end()) {
    fromRawValue(context, tmp_pitch->second, result.pitch);
  }
  auto tmp_zoom = map.find("zoom");
  if (tmp_zoom != map.end()) {
    fromRawValue(context, tmp_zoom->second, result.zoom);
  }
}

static inline std::string toString(const RNMapsMapViewInitialCameraStruct &value) {
  return "[Object RNMapsMapViewInitialCameraStruct]";
}

struct RNMapsMapViewInitialRegionStruct {
  double latitude{0.0};
  double longitude{0.0};
  double latitudeDelta{0.0};
  double longitudeDelta{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewInitialRegionStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
  auto tmp_latitudeDelta = map.find("latitudeDelta");
  if (tmp_latitudeDelta != map.end()) {
    fromRawValue(context, tmp_latitudeDelta->second, result.latitudeDelta);
  }
  auto tmp_longitudeDelta = map.find("longitudeDelta");
  if (tmp_longitudeDelta != map.end()) {
    fromRawValue(context, tmp_longitudeDelta->second, result.longitudeDelta);
  }
}

static inline std::string toString(const RNMapsMapViewInitialRegionStruct &value) {
  return "[Object RNMapsMapViewInitialRegionStruct]";
}

struct RNMapsMapViewLegalLabelInsetsStruct {
  double top{0.0};
  double right{0.0};
  double bottom{0.0};
  double left{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewLegalLabelInsetsStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_top = map.find("top");
  if (tmp_top != map.end()) {
    fromRawValue(context, tmp_top->second, result.top);
  }
  auto tmp_right = map.find("right");
  if (tmp_right != map.end()) {
    fromRawValue(context, tmp_right->second, result.right);
  }
  auto tmp_bottom = map.find("bottom");
  if (tmp_bottom != map.end()) {
    fromRawValue(context, tmp_bottom->second, result.bottom);
  }
  auto tmp_left = map.find("left");
  if (tmp_left != map.end()) {
    fromRawValue(context, tmp_left->second, result.left);
  }
}

static inline std::string toString(const RNMapsMapViewLegalLabelInsetsStruct &value) {
  return "[Object RNMapsMapViewLegalLabelInsetsStruct]";
}

struct RNMapsMapViewMapPaddingStruct {
  double top{0.0};
  double right{0.0};
  double bottom{0.0};
  double left{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewMapPaddingStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_top = map.find("top");
  if (tmp_top != map.end()) {
    fromRawValue(context, tmp_top->second, result.top);
  }
  auto tmp_right = map.find("right");
  if (tmp_right != map.end()) {
    fromRawValue(context, tmp_right->second, result.right);
  }
  auto tmp_bottom = map.find("bottom");
  if (tmp_bottom != map.end()) {
    fromRawValue(context, tmp_bottom->second, result.bottom);
  }
  auto tmp_left = map.find("left");
  if (tmp_left != map.end()) {
    fromRawValue(context, tmp_left->second, result.left);
  }
}

static inline std::string toString(const RNMapsMapViewMapPaddingStruct &value) {
  return "[Object RNMapsMapViewMapPaddingStruct]";
}

struct RNMapsMapViewRegionStruct {
  double latitude{0.0};
  double longitude{0.0};
  double latitudeDelta{0.0};
  double longitudeDelta{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewRegionStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
  auto tmp_latitudeDelta = map.find("latitudeDelta");
  if (tmp_latitudeDelta != map.end()) {
    fromRawValue(context, tmp_latitudeDelta->second, result.latitudeDelta);
  }
  auto tmp_longitudeDelta = map.find("longitudeDelta");
  if (tmp_longitudeDelta != map.end()) {
    fromRawValue(context, tmp_longitudeDelta->second, result.longitudeDelta);
  }
}

static inline std::string toString(const RNMapsMapViewRegionStruct &value) {
  return "[Object RNMapsMapViewRegionStruct]";
}

struct RNMapsMapViewCameraZoomRangeStruct {
  double minCenterCoordinateDistance{0.0};
  double maxCenterCoordinateDistance{0.0};
  bool animated{false};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMapViewCameraZoomRangeStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_minCenterCoordinateDistance = map.find("minCenterCoordinateDistance");
  if (tmp_minCenterCoordinateDistance != map.end()) {
    fromRawValue(context, tmp_minCenterCoordinateDistance->second, result.minCenterCoordinateDistance);
  }
  auto tmp_maxCenterCoordinateDistance = map.find("maxCenterCoordinateDistance");
  if (tmp_maxCenterCoordinateDistance != map.end()) {
    fromRawValue(context, tmp_maxCenterCoordinateDistance->second, result.maxCenterCoordinateDistance);
  }
  auto tmp_animated = map.find("animated");
  if (tmp_animated != map.end()) {
    fromRawValue(context, tmp_animated->second, result.animated);
  }
}

static inline std::string toString(const RNMapsMapViewCameraZoomRangeStruct &value) {
  return "[Object RNMapsMapViewCameraZoomRangeStruct]";
}
class RNMapsMapViewProps final : public ViewProps {
 public:
  RNMapsMapViewProps() = default;
  RNMapsMapViewProps(const PropsParserContext& context, const RNMapsMapViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool cacheEnabled{false};
  RNMapsMapViewCameraStruct camera{};
  RNMapsMapViewCompassOffsetStruct compassOffset{};
  bool followsUserLocation{false};
  bool poiClickEnabled{false};
  RNMapsMapViewInitialCameraStruct initialCamera{};
  RNMapsMapViewInitialRegionStruct initialRegion{};
  std::string kmlSrc{};
  RNMapsMapViewLegalLabelInsetsStruct legalLabelInsets{};
  bool liteMode{false};
  std::string googleMapId{};
  RNMapsMapViewGoogleRenderer googleRenderer{RNMapsMapViewGoogleRenderer::LATEST};
  SharedColor loadingBackgroundColor{};
  bool loadingEnabled{false};
  SharedColor loadingIndicatorColor{};
  RNMapsMapViewMapPaddingStruct mapPadding{};
  RNMapsMapViewMapType mapType{RNMapsMapViewMapType::Standard};
  double maxDelta{0.0};
  Float maxZoom{0.0};
  double minDelta{0.0};
  Float minZoom{0.0};
  bool moveOnMarkerPress{true};
  bool handlePanDrag{false};
  RNMapsMapViewPaddingAdjustmentBehavior paddingAdjustmentBehavior{RNMapsMapViewPaddingAdjustmentBehavior::Never};
  bool pitchEnabled{true};
  RNMapsMapViewRegionStruct region{};
  bool rotateEnabled{true};
  bool scrollDuringRotateOrZoomEnabled{true};
  bool scrollEnabled{true};
  bool showsBuildings{true};
  bool showsCompass{true};
  bool showsIndoorLevelPicker{false};
  bool showsIndoors{true};
  bool showsMyLocationButton{true};
  bool showsScale{false};
  bool showsUserLocation{false};
  SharedColor tintColor{};
  bool toolbarEnabled{true};
  RNMapsMapViewUserInterfaceStyle userInterfaceStyle{RNMapsMapViewUserInterfaceStyle::System};
  std::string customMapStyleString{};
  std::string userLocationAnnotationTitle{};
  bool userLocationCalloutEnabled{false};
  int userLocationFastestInterval{5000};
  RNMapsMapViewUserLocationPriority userLocationPriority{RNMapsMapViewUserLocationPriority::High};
  int userLocationUpdateInterval{5000};
  bool zoomControlEnabled{true};
  bool zoomEnabled{true};
  bool showsTraffic{false};
  bool zoomTapEnabled{true};
  RNMapsMapViewCameraZoomRangeStruct cameraZoomRange{};
};

enum class RNMapsMarkerDisplayPriority { Required, High, Low };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMarkerDisplayPriority &result) {
  auto string = (std::string)value;
  if (string == "required") { result = RNMapsMarkerDisplayPriority::Required; return; }
  if (string == "high") { result = RNMapsMarkerDisplayPriority::High; return; }
  if (string == "low") { result = RNMapsMarkerDisplayPriority::Low; return; }
  abort();
}

static inline std::string toString(const RNMapsMarkerDisplayPriority &value) {
  switch (value) {
    case RNMapsMarkerDisplayPriority::Required: return "required";
    case RNMapsMarkerDisplayPriority::High: return "high";
    case RNMapsMarkerDisplayPriority::Low: return "low";
  }
}
enum class RNMapsMarkerTitleVisibility { Hidden, Adaptive, Visible };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMarkerTitleVisibility &result) {
  auto string = (std::string)value;
  if (string == "hidden") { result = RNMapsMarkerTitleVisibility::Hidden; return; }
  if (string == "adaptive") { result = RNMapsMarkerTitleVisibility::Adaptive; return; }
  if (string == "visible") { result = RNMapsMarkerTitleVisibility::Visible; return; }
  abort();
}

static inline std::string toString(const RNMapsMarkerTitleVisibility &value) {
  switch (value) {
    case RNMapsMarkerTitleVisibility::Hidden: return "hidden";
    case RNMapsMarkerTitleVisibility::Adaptive: return "adaptive";
    case RNMapsMarkerTitleVisibility::Visible: return "visible";
  }
}
enum class RNMapsMarkerSubtitleVisibility { Hidden, Adaptive, Visible };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMarkerSubtitleVisibility &result) {
  auto string = (std::string)value;
  if (string == "hidden") { result = RNMapsMarkerSubtitleVisibility::Hidden; return; }
  if (string == "adaptive") { result = RNMapsMarkerSubtitleVisibility::Adaptive; return; }
  if (string == "visible") { result = RNMapsMarkerSubtitleVisibility::Visible; return; }
  abort();
}

static inline std::string toString(const RNMapsMarkerSubtitleVisibility &value) {
  switch (value) {
    case RNMapsMarkerSubtitleVisibility::Hidden: return "hidden";
    case RNMapsMarkerSubtitleVisibility::Adaptive: return "adaptive";
    case RNMapsMarkerSubtitleVisibility::Visible: return "visible";
  }
}
struct RNMapsMarkerAnchorStruct {
  double x{0.0};
  double y{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMarkerAnchorStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_x = map.find("x");
  if (tmp_x != map.end()) {
    fromRawValue(context, tmp_x->second, result.x);
  }
  auto tmp_y = map.find("y");
  if (tmp_y != map.end()) {
    fromRawValue(context, tmp_y->second, result.y);
  }
}

static inline std::string toString(const RNMapsMarkerAnchorStruct &value) {
  return "[Object RNMapsMarkerAnchorStruct]";
}

struct RNMapsMarkerCalloutAnchorStruct {
  double x{0.0};
  double y{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMarkerCalloutAnchorStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_x = map.find("x");
  if (tmp_x != map.end()) {
    fromRawValue(context, tmp_x->second, result.x);
  }
  auto tmp_y = map.find("y");
  if (tmp_y != map.end()) {
    fromRawValue(context, tmp_y->second, result.y);
  }
}

static inline std::string toString(const RNMapsMarkerCalloutAnchorStruct &value) {
  return "[Object RNMapsMarkerCalloutAnchorStruct]";
}

struct RNMapsMarkerCalloutOffsetStruct {
  double x{0.0};
  double y{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMarkerCalloutOffsetStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_x = map.find("x");
  if (tmp_x != map.end()) {
    fromRawValue(context, tmp_x->second, result.x);
  }
  auto tmp_y = map.find("y");
  if (tmp_y != map.end()) {
    fromRawValue(context, tmp_y->second, result.y);
  }
}

static inline std::string toString(const RNMapsMarkerCalloutOffsetStruct &value) {
  return "[Object RNMapsMarkerCalloutOffsetStruct]";
}

struct RNMapsMarkerCoordinateStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsMarkerCoordinateStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsMarkerCoordinateStruct &value) {
  return "[Object RNMapsMarkerCoordinateStruct]";
}
class RNMapsMarkerProps final : public ViewProps {
 public:
  RNMapsMarkerProps() = default;
  RNMapsMarkerProps(const PropsParserContext& context, const RNMapsMarkerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  RNMapsMarkerAnchorStruct anchor{};
  RNMapsMarkerCalloutAnchorStruct calloutAnchor{};
  ImageSource image{};
  RNMapsMarkerCalloutOffsetStruct calloutOffset{};
  RNMapsMarkerDisplayPriority displayPriority{RNMapsMarkerDisplayPriority::Required};
  RNMapsMarkerCoordinateStruct coordinate{};
  std::string description{};
  bool draggable{false};
  std::string title{};
  std::string identifier{};
  bool isPreselected{false};
  double opacity{1.0};
  SharedColor pinColor{};
  RNMapsMarkerTitleVisibility titleVisibility{RNMapsMarkerTitleVisibility::Visible};
  RNMapsMarkerSubtitleVisibility subtitleVisibility{RNMapsMarkerSubtitleVisibility::Adaptive};
  bool useLegacyPinView{false};
};

struct RNMapsOverlayBoundsNorthEastStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsOverlayBoundsNorthEastStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsOverlayBoundsNorthEastStruct &value) {
  return "[Object RNMapsOverlayBoundsNorthEastStruct]";
}

struct RNMapsOverlayBoundsSouthWestStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsOverlayBoundsSouthWestStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsOverlayBoundsSouthWestStruct &value) {
  return "[Object RNMapsOverlayBoundsSouthWestStruct]";
}

struct RNMapsOverlayBoundsStruct {
  RNMapsOverlayBoundsNorthEastStruct northEast{};
  RNMapsOverlayBoundsSouthWestStruct southWest{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsOverlayBoundsStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_northEast = map.find("northEast");
  if (tmp_northEast != map.end()) {
    fromRawValue(context, tmp_northEast->second, result.northEast);
  }
  auto tmp_southWest = map.find("southWest");
  if (tmp_southWest != map.end()) {
    fromRawValue(context, tmp_southWest->second, result.southWest);
  }
}

static inline std::string toString(const RNMapsOverlayBoundsStruct &value) {
  return "[Object RNMapsOverlayBoundsStruct]";
}
class RNMapsOverlayProps final : public ViewProps {
 public:
  RNMapsOverlayProps() = default;
  RNMapsOverlayProps(const PropsParserContext& context, const RNMapsOverlayProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  Float bearing{0.0};
  RNMapsOverlayBoundsStruct bounds{};
  ImageSource image{};
  Float opacity{1.0};
  bool tappable{false};
};

enum class RNMapsPolylineLineCap { Butt, Round, Square };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsPolylineLineCap &result) {
  auto string = (std::string)value;
  if (string == "butt") { result = RNMapsPolylineLineCap::Butt; return; }
  if (string == "round") { result = RNMapsPolylineLineCap::Round; return; }
  if (string == "square") { result = RNMapsPolylineLineCap::Square; return; }
  abort();
}

static inline std::string toString(const RNMapsPolylineLineCap &value) {
  switch (value) {
    case RNMapsPolylineLineCap::Butt: return "butt";
    case RNMapsPolylineLineCap::Round: return "round";
    case RNMapsPolylineLineCap::Square: return "square";
  }
}
enum class RNMapsPolylineLineJoin { Miter, Round, Bevel };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsPolylineLineJoin &result) {
  auto string = (std::string)value;
  if (string == "miter") { result = RNMapsPolylineLineJoin::Miter; return; }
  if (string == "round") { result = RNMapsPolylineLineJoin::Round; return; }
  if (string == "bevel") { result = RNMapsPolylineLineJoin::Bevel; return; }
  abort();
}

static inline std::string toString(const RNMapsPolylineLineJoin &value) {
  switch (value) {
    case RNMapsPolylineLineJoin::Miter: return "miter";
    case RNMapsPolylineLineJoin::Round: return "round";
    case RNMapsPolylineLineJoin::Bevel: return "bevel";
  }
}
struct RNMapsPolylineCoordinatesStruct {
  double latitude{0.0};
  double longitude{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNMapsPolylineCoordinatesStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_latitude = map.find("latitude");
  if (tmp_latitude != map.end()) {
    fromRawValue(context, tmp_latitude->second, result.latitude);
  }
  auto tmp_longitude = map.find("longitude");
  if (tmp_longitude != map.end()) {
    fromRawValue(context, tmp_longitude->second, result.longitude);
  }
}

static inline std::string toString(const RNMapsPolylineCoordinatesStruct &value) {
  return "[Object RNMapsPolylineCoordinatesStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<RNMapsPolylineCoordinatesStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    RNMapsPolylineCoordinatesStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}

class RNMapsPolylineProps final : public ViewProps {
 public:
  RNMapsPolylineProps() = default;
  RNMapsPolylineProps(const PropsParserContext& context, const RNMapsPolylineProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::vector<RNMapsPolylineCoordinatesStruct> coordinates{};
  bool geodesic{false};
  RNMapsPolylineLineCap lineCap{RNMapsPolylineLineCap::Butt};
  std::vector<double> lineDashPattern{};
  RNMapsPolylineLineJoin lineJoin{RNMapsPolylineLineJoin::Miter};
  SharedColor strokeColor{};
  std::vector<SharedColor> strokeColors{};
  Float strokeWidth{0.0};
  bool tappable{false};
};

class RNMapsUrlTileProps final : public ViewProps {
 public:
  RNMapsUrlTileProps() = default;
  RNMapsUrlTileProps(const PropsParserContext& context, const RNMapsUrlTileProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool doubleTileSize{false};
  bool flipY{false};
  int maximumNativeZ{100};
  int maximumZ{100};
  int minimumZ{0};
  bool offlineMode{false};
  bool shouldReplaceMapContent{false};
  int tileCacheMaxAge{0};
  std::string tileCachePath{};
  int tileSize{256};
  std::string urlTemplate{};
};

class RNMapsWMSTileProps final : public ViewProps {
 public:
  RNMapsWMSTileProps() = default;
  RNMapsWMSTileProps(const PropsParserContext& context, const RNMapsWMSTileProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  int maximumNativeZ{100};
  int maximumZ{100};
  int minimumZ{0};
  bool offlineMode{false};
  bool shouldReplaceMapContent{false};
  int tileCacheMaxAge{0};
  std::string tileCachePath{};
  int tileSize{256};
  std::string urlTemplate{};
};

} // namespace facebook::react
