/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  
#pragma mark - NativeAirMapsModuleLatLng

template <typename P0, typename P1>
struct NativeAirMapsModuleLatLng {
  P0 latitude;
  P1 longitude;
  bool operator==(const NativeAirMapsModuleLatLng &other) const {
    return latitude == other.latitude && longitude == other.longitude;
  }
};

template <typename T>
struct NativeAirMapsModuleLatLngBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.latitude)>(rt, value.getProperty(rt, "latitude"), jsInvoker),
      bridging::fromJs<decltype(types.longitude)>(rt, value.getProperty(rt, "longitude"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double latitudeToJs(jsi::Runtime &rt, decltype(types.latitude) value) {
    return bridging::toJs(rt, value);
  }

  static double longitudeToJs(jsi::Runtime &rt, decltype(types.longitude) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "latitude", bridging::toJs(rt, value.latitude, jsInvoker));
    result.setProperty(rt, "longitude", bridging::toJs(rt, value.longitude, jsInvoker));
    return result;
  }
};



#pragma mark - NativeAirMapsModuleMapBoundaries

template <typename P0, typename P1>
struct NativeAirMapsModuleMapBoundaries {
  P0 northEast;
  P1 southWest;
  bool operator==(const NativeAirMapsModuleMapBoundaries &other) const {
    return northEast == other.northEast && southWest == other.southWest;
  }
};

template <typename T>
struct NativeAirMapsModuleMapBoundariesBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.northEast)>(rt, value.getProperty(rt, "northEast"), jsInvoker),
      bridging::fromJs<decltype(types.southWest)>(rt, value.getProperty(rt, "southWest"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Object northEastToJs(jsi::Runtime &rt, decltype(types.northEast) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object southWestToJs(jsi::Runtime &rt, decltype(types.southWest) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "northEast", bridging::toJs(rt, value.northEast, jsInvoker));
    result.setProperty(rt, "southWest", bridging::toJs(rt, value.southWest, jsInvoker));
    return result;
  }
};



#pragma mark - NativeAirMapsModulePoint

template <typename P0, typename P1>
struct NativeAirMapsModulePoint {
  P0 x;
  P1 y;
  bool operator==(const NativeAirMapsModulePoint &other) const {
    return x == other.x && y == other.y;
  }
};

template <typename T>
struct NativeAirMapsModulePointBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.x)>(rt, value.getProperty(rt, "x"), jsInvoker),
      bridging::fromJs<decltype(types.y)>(rt, value.getProperty(rt, "y"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double xToJs(jsi::Runtime &rt, decltype(types.x) value) {
    return bridging::toJs(rt, value);
  }

  static double yToJs(jsi::Runtime &rt, decltype(types.y) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "x", bridging::toJs(rt, value.x, jsInvoker));
    result.setProperty(rt, "y", bridging::toJs(rt, value.y, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeAirMapsModuleCxxSpecJSI : public TurboModule {
protected:
  NativeAirMapsModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value getCamera(jsi::Runtime &rt, double tag) = 0;
  virtual jsi::Value getMarkersFrames(jsi::Runtime &rt, double tag, bool onlyVisible) = 0;
  virtual jsi::Value getMapBoundaries(jsi::Runtime &rt, double tag) = 0;
  virtual jsi::Value takeSnapshot(jsi::Runtime &rt, double tag, jsi::String config) = 0;
  virtual jsi::Value getAddressFromCoordinates(jsi::Runtime &rt, double tag, jsi::Object coordinate) = 0;
  virtual jsi::Value getPointForCoordinate(jsi::Runtime &rt, double tag, jsi::Object coordinate) = 0;
  virtual jsi::Value getCoordinateForPoint(jsi::Runtime &rt, double tag, jsi::Object point) = 0;

};

template <typename T>
class JSI_EXPORT NativeAirMapsModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNMapsAirModule";

protected:
  NativeAirMapsModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAirMapsModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAirMapsModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAirMapsModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value getCamera(jsi::Runtime &rt, double tag) override {
      static_assert(
          bridging::getParameterCount(&T::getCamera) == 2,
          "Expected getCamera(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getCamera, jsInvoker_, instance_, std::move(tag));
    }
    jsi::Value getMarkersFrames(jsi::Runtime &rt, double tag, bool onlyVisible) override {
      static_assert(
          bridging::getParameterCount(&T::getMarkersFrames) == 3,
          "Expected getMarkersFrames(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getMarkersFrames, jsInvoker_, instance_, std::move(tag), std::move(onlyVisible));
    }
    jsi::Value getMapBoundaries(jsi::Runtime &rt, double tag) override {
      static_assert(
          bridging::getParameterCount(&T::getMapBoundaries) == 2,
          "Expected getMapBoundaries(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getMapBoundaries, jsInvoker_, instance_, std::move(tag));
    }
    jsi::Value takeSnapshot(jsi::Runtime &rt, double tag, jsi::String config) override {
      static_assert(
          bridging::getParameterCount(&T::takeSnapshot) == 3,
          "Expected takeSnapshot(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::takeSnapshot, jsInvoker_, instance_, std::move(tag), std::move(config));
    }
    jsi::Value getAddressFromCoordinates(jsi::Runtime &rt, double tag, jsi::Object coordinate) override {
      static_assert(
          bridging::getParameterCount(&T::getAddressFromCoordinates) == 3,
          "Expected getAddressFromCoordinates(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getAddressFromCoordinates, jsInvoker_, instance_, std::move(tag), std::move(coordinate));
    }
    jsi::Value getPointForCoordinate(jsi::Runtime &rt, double tag, jsi::Object coordinate) override {
      static_assert(
          bridging::getParameterCount(&T::getPointForCoordinate) == 3,
          "Expected getPointForCoordinate(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getPointForCoordinate, jsInvoker_, instance_, std::move(tag), std::move(coordinate));
    }
    jsi::Value getCoordinateForPoint(jsi::Runtime &rt, double tag, jsi::Object point) override {
      static_assert(
          bridging::getParameterCount(&T::getCoordinateForPoint) == 3,
          "Expected getCoordinateForPoint(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getCoordinateForPoint, jsInvoker_, instance_, std::move(tag), std::move(point));
    }

  private:
    friend class NativeAirMapsModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
