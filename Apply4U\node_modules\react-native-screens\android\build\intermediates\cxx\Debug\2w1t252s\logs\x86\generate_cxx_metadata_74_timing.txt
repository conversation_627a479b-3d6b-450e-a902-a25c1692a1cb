# C/C++ build system timings
generate_cxx_metadata 22ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 32ms
  [gap of 12ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 81ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
  [gap of 22ms]
generate_cxx_metadata completed in 38ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 34ms
  [gap of 13ms]
generate_cxx_metadata completed in 63ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 34ms
  [gap of 11ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 83ms

# C/C++ build system timings
generate_cxx_metadata 27ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 31ms
  [gap of 11ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 77ms

