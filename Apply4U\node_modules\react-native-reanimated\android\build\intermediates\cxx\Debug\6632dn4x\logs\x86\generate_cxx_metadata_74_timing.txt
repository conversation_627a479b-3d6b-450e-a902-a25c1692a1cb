# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
  [gap of 14ms]
generate_cxx_metadata completed in 30ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 37ms
  [gap of 17ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 93ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 40ms
  [gap of 13ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 92ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 19ms
  [gap of 24ms]
generate_cxx_metadata completed in 46ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 36ms
  [gap of 13ms]
generate_cxx_metadata completed in 68ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 42ms
  [gap of 12ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 91ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 44ms
  [gap of 13ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 94ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 37ms
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 76ms

