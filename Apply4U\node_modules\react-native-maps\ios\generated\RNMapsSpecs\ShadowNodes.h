
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include "EventEmitters.h"
#include "Props.h"
#include "States.h"
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char RNMapsCalloutComponentName[];

/*
 * `ShadowNode` for <RNMapsCallout> component.
 */
using RNMapsCalloutShadowNode = ConcreteViewShadowNode<
    RNMapsCalloutComponentName,
    RNMapsCalloutProps,
    RNMapsCalloutEventEmitter,
    RNMapsCalloutState>;

JSI_EXPORT extern const char RNMapsCircleComponentName[];

/*
 * `ShadowNode` for <RNMapsCircle> component.
 */
using RNMapsCircleShadowNode = ConcreteViewShadowNode<
    RNMapsCircleComponentName,
    RNMapsCircleProps,
    RNMapsCircleEventEmitter,
    RNMapsCircleState>;

JSI_EXPORT extern const char RNMapsGoogleMapViewComponentName[];

/*
 * `ShadowNode` for <RNMapsGoogleMapView> component.
 */
using RNMapsGoogleMapViewShadowNode = ConcreteViewShadowNode<
    RNMapsGoogleMapViewComponentName,
    RNMapsGoogleMapViewProps,
    RNMapsGoogleMapViewEventEmitter,
    RNMapsGoogleMapViewState>;

JSI_EXPORT extern const char RNMapsGooglePolygonComponentName[];

/*
 * `ShadowNode` for <RNMapsGooglePolygon> component.
 */
using RNMapsGooglePolygonShadowNode = ConcreteViewShadowNode<
    RNMapsGooglePolygonComponentName,
    RNMapsGooglePolygonProps,
    RNMapsGooglePolygonEventEmitter,
    RNMapsGooglePolygonState>;

JSI_EXPORT extern const char RNMapsMapViewComponentName[];

/*
 * `ShadowNode` for <RNMapsMapView> component.
 */
using RNMapsMapViewShadowNode = ConcreteViewShadowNode<
    RNMapsMapViewComponentName,
    RNMapsMapViewProps,
    RNMapsMapViewEventEmitter,
    RNMapsMapViewState>;

JSI_EXPORT extern const char RNMapsMarkerComponentName[];

/*
 * `ShadowNode` for <RNMapsMarker> component.
 */
using RNMapsMarkerShadowNode = ConcreteViewShadowNode<
    RNMapsMarkerComponentName,
    RNMapsMarkerProps,
    RNMapsMarkerEventEmitter,
    RNMapsMarkerState>;

JSI_EXPORT extern const char RNMapsOverlayComponentName[];

/*
 * `ShadowNode` for <RNMapsOverlay> component.
 */
using RNMapsOverlayShadowNode = ConcreteViewShadowNode<
    RNMapsOverlayComponentName,
    RNMapsOverlayProps,
    RNMapsOverlayEventEmitter,
    RNMapsOverlayState>;

JSI_EXPORT extern const char RNMapsPolylineComponentName[];

/*
 * `ShadowNode` for <RNMapsPolyline> component.
 */
using RNMapsPolylineShadowNode = ConcreteViewShadowNode<
    RNMapsPolylineComponentName,
    RNMapsPolylineProps,
    RNMapsPolylineEventEmitter,
    RNMapsPolylineState>;

JSI_EXPORT extern const char RNMapsUrlTileComponentName[];

/*
 * `ShadowNode` for <RNMapsUrlTile> component.
 */
using RNMapsUrlTileShadowNode = ConcreteViewShadowNode<
    RNMapsUrlTileComponentName,
    RNMapsUrlTileProps,
    RNMapsUrlTileEventEmitter,
    RNMapsUrlTileState>;

JSI_EXPORT extern const char RNMapsWMSTileComponentName[];

/*
 * `ShadowNode` for <RNMapsWMSTile> component.
 */
using RNMapsWMSTileShadowNode = ConcreteViewShadowNode<
    RNMapsWMSTileComponentName,
    RNMapsWMSTileProps,
    RNMapsWMSTileEventEmitter,
    RNMapsWMSTileState>;

} // namespace facebook::react
