# C/C++ build system timings
generate_cxx_metadata
  [gap of 60ms]
  create-invalidation-state 136ms
  [gap of 48ms]
  write-metadata-json-to-file 34ms
generate_cxx_metadata completed in 279ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 104ms
  [gap of 72ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 219ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 50ms]
  create-invalidation-state 101ms
  [gap of 35ms]
  write-metadata-json-to-file 24ms
generate_cxx_metadata completed in 211ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 47ms]
  create-invalidation-state 96ms
  [gap of 60ms]
  write-metadata-json-to-file 60ms
generate_cxx_metadata completed in 263ms

