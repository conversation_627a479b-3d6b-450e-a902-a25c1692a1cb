# C/C++ build system timings
generate_cxx_metadata 23ms

# C/C++ build system timings
generate_cxx_metadata 26ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 39ms
  [gap of 10ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 95ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 201ms
  [gap of 100ms]
  write-metadata-json-to-file 39ms
generate_cxx_metadata completed in 373ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 36ms
  [gap of 11ms]
  write-metadata-json-to-file 32ms
generate_cxx_metadata completed in 100ms

# C/C++ build system timings
generate_cxx_metadata 28ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 44ms
  [gap of 13ms]
  write-metadata-json-to-file 39ms
generate_cxx_metadata completed in 122ms

