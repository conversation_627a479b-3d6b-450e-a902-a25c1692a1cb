
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "RNMapsSpecs.h"

namespace facebook::react {

static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getCamera(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, PromiseKind, "getCamera", "(DLcom/facebook/react/bridge/Promise;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getMarkersFrames(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, PromiseKind, "getMarkersFrames", "(DZLcom/facebook/react/bridge/Promise;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getMapBoundaries(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, PromiseKind, "getMapBoundaries", "(DLcom/facebook/react/bridge/Promise;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_takeSnapshot(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, PromiseKind, "takeSnapshot", "(DLjava/lang/String;Lcom/facebook/react/bridge/Promise;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getAddressFromCoordinates(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, PromiseKind, "getAddressFromCoordinates", "(DLcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Promise;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getPointForCoordinate(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, PromiseKind, "getPointForCoordinate", "(DLcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Promise;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAirMapsModuleSpecJSI_getCoordinateForPoint(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, PromiseKind, "getCoordinateForPoint", "(DLcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Promise;)V", args, count, cachedMethodId);
}

NativeAirMapsModuleSpecJSI::NativeAirMapsModuleSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["getCamera"] = MethodMetadata {1, __hostFunction_NativeAirMapsModuleSpecJSI_getCamera};
  methodMap_["getMarkersFrames"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_getMarkersFrames};
  methodMap_["getMapBoundaries"] = MethodMetadata {1, __hostFunction_NativeAirMapsModuleSpecJSI_getMapBoundaries};
  methodMap_["takeSnapshot"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_takeSnapshot};
  methodMap_["getAddressFromCoordinates"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_getAddressFromCoordinates};
  methodMap_["getPointForCoordinate"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_getPointForCoordinate};
  methodMap_["getCoordinateForPoint"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleSpecJSI_getCoordinateForPoint};
}

std::shared_ptr<TurboModule> RNMapsSpecs_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "RNMapsAirModule") {
    return std::make_shared<NativeAirMapsModuleSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
