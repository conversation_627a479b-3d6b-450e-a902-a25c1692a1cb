{"name": "react-native-maps", "description": "React Native Mapview component for iOS + Android", "source": "src/index", "main": "src/index.ts", "author": "<PERSON><PERSON> <leland.m.rich<PERSON><EMAIL>>", "homepage": "https://github.com/react-native-maps/react-native-maps#readme", "version": "1.24.3", "license": "MIT", "scripts": {"lint": "eslint . --max-warnings 0", "tscheck": "tsc --noEmit", "format-check": "prettier --check .", "build": "tsc --project tsconfig.build.json && tsc --project plugin/tsconfig.json", "test": "jest", "prepare": "husky", "release": "semantic-release", "bundle-install": "cd example && bundle install", "pod-install": "cd example/ios && RCT_NEW_ARCH_ENABLED=1 bundle exec pod install", "bootstrap": "yarn --cwd example && yarn && yarn bundle-install && yarn pod-install"}, "files": ["src", "android", "ios", "app.plugin.js", "plugin/build", "react-native-maps.podspec", "react-native.config.js", "!android/build", "!ios/build", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__"], "repository": {"type": "git", "url": "https://github.com/react-native-maps/react-native-maps"}, "keywords": ["react", "react-native", "react-component", "map", "mapview", "google-maps", "mapkit"], "peerDependencies": {"react": ">= 18.3.1", "react-native": ">= 0.76.0", "react-native-web": ">= 0.11"}, "peerDependenciesMeta": {"react-native-web": {"optional": true}}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@expo/config-plugins": "^9.0.17", "@react-native-community/cli": "15.0.1", "@react-native/babel-preset": "0.76.8", "@react-native/eslint-config": "0.76.8", "@react-native/metro-config": "0.76.8", "@react-native/typescript-config": "0.76.8", "@semantic-release/changelog": "6.0.3", "@semantic-release/git": "10.0.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "husky": "9.1.7", "jest": "^29.6.3", "prettier": "2.8.8", "react": "^18.3.1", "react-native": "^0.77.1", "react-test-renderer": "18.3.1", "semantic-release": "24.2.3", "typescript": "5.0.4"}, "dependencies": {"@types/geojson": "^7946.0.13"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "jest": {"preset": "react-native", "testPathIgnorePatterns": ["/node_modules/", "e2e"]}, "codegenConfig": {"name": "RNMapsSpecs", "type": "all", "jsSrcsDir": "./src/specs", "outputDir": {"ios": "ios/generated", "android": "android/src/main"}, "includesGeneratedCode": true, "android": {"javaPackageName": "com.rnmaps.fabric"}}, "engines": {"node": ">=18"}}