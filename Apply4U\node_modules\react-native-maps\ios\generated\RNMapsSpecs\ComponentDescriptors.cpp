
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateComponentDescriptorCpp.js
 */

#include "ComponentDescriptors.h"
#include <react/renderer/core/ConcreteComponentDescriptor.h>
#include <react/renderer/componentregistry/ComponentDescriptorProviderRegistry.h>

namespace facebook::react {

void RNMapsSpecs_registerComponentDescriptorsFromCodegen(
  std::shared_ptr<const ComponentDescriptorProviderRegistry> registry) {
registry->add(concreteComponentDescriptorProvider<RNMapsCalloutComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<RNMapsCircleComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<RNMapsGoogleMapViewComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<RNMapsGooglePolygonComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<RNMapsMapViewComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<RNMapsMarkerComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<RNMapsOverlayComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<RNMapsPolylineComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<RNMapsUrlTileComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<RNMapsWMSTileComponentDescriptor>());
}

} // namespace facebook::react
