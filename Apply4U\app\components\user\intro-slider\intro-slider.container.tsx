import React from 'react';
import {introSlideOne, introSlideThree, introSlideTwo, introSlideFour, introSlideFive,introSlideOneFeatureIconOne,introSlideOneFeatureIconTwo,introSlideOneFeatureIconThree,introSlideTwoFeatureIconOne,introSlideTwoFeatureIconTwo,introSlideTwoFeatureIconThree,introSlideThreeFeatureIconOne,introSlideThreeFeatureIconTwo,introSlideThreeFeatureIconThree,introSlideFourFeatureIconOne,introSlideFourFeatureIconTwo,introSlideFourFeatureIconThree,introSlideFiveFeatureIconOne,introSlideFiveFeatureIconTwo,introSlideFiveFeatureIconThree} from '../../../assets';
import {IIntroSliderPage} from '../../../interfaces/user/slider-page.interface';
import {IntroSliderComponent} from '../../common';
import {connect} from 'react-redux';
import {setIsFirstVisitAction} from '../../../redux/actions';
import {IApplicationState} from '../../../redux';
import {IAction} from '../../../redux/actions/action.interface';
import {navigate} from '../../../utility';
import {screens} from '../../../app.constant';

interface IProps {
  setIsFirstTime: (payload: boolean) => IAction<boolean>;
  isFirstVisit: boolean;
}
const IntroSliderContainer = (props: IProps) => {
  const onDone = () => {
    props.setIsFirstTime(false);
    navigate(screens.Login);
  };
  const pages: IIntroSliderPage[] = [
    {
      image: introSlideOne,
      title: 'Welcome',
      titleStyle: {fontSize: 32,fontFamily: 'Poppins-Bold'},
      description:
        "Your smarter job hunting starts here.",
      featurePoints:[
        {
          icon:introSlideOneFeatureIconOne,
          text:'Find the right job faster with smart matching technology.'
        },
        {
          icon:introSlideOneFeatureIconTwo,
          text:'Boost your resume with expert-driven optimization.'
        },
        {
          icon:introSlideOneFeatureIconThree,
          text:'Practice and perfect your interview skills with interactive coaching.'
        }
      ]
    },
    {
      image: introSlideTwo,
      title: 'Stand Out',
      titleStyle: {fontSize: 32,fontFamily: 'Poppins-Bold'},
      description:
        "Let our AI fine-tune your CV for better visibility and results.",
      featurePoints:[
        {
          icon:introSlideTwoFeatureIconOne,
          text:'AI-enhanced formatting and structure for a professional finish.'
        },
        {
          icon:introSlideTwoFeatureIconTwo,
          text:'Smart keyword integration to pass ATS filters.'
        },
        {
          icon:introSlideTwoFeatureIconThree,
          text:'Error-free grammar and style adjustments in seconds.'
        }
      ]
    },
    {
      image: introSlideThree,
      title: 'Track Your Applications',
      titleStyle: {fontSize: 30,fontFamily: 'Poppins-Bold'},
      description:
        "Stay in the loop every step of the way with real-time updates and insights.",
      featurePoints:[
        {
          icon:introSlideThreeFeatureIconOne,
          text:'Get notified when your CV is viewed or shortlisted.'
        },
        {
          icon:introSlideThreeFeatureIconTwo,
          text:'Track every stage, from applied to interview.'
        },
        {
          icon:introSlideThreeFeatureIconThree,
          text:'AI insights to help you follow up at the right time.'
        }
      ]
    },
    {
      image: introSlideFour,
      title: 'Jobs That Fit You',
      titleStyle: {fontSize: 32,fontFamily: 'Poppins-Bold'},
      description:
        "Let your AI assistant find high-match roles tailored to your skills and preferences.",
      featurePoints:[
        {
          icon:introSlideFourFeatureIconOne,
          text:'Receive job suggestions based on your profile and career goals.'
        },
        {
          icon:introSlideFourFeatureIconTwo,
          text:'Match scores to help you focus on the best-fit roles.'
        },
        {
          icon:introSlideFourFeatureIconThree,
          text:'No endless scrolling, just relevant opportunities, fast.'
        }
      ]
    },
    {
      image: introSlideFive,
      title: 'Prep Smarter',
      titleStyle: {fontSize: 32,fontFamily: 'Poppins-Bold'},
      description:
        "Get confident, get feedback, and get hired all with your AI coach by your side.",
      featurePoints:[
        {
          icon:introSlideFiveFeatureIconOne,
          text:'Practice real interview questions tailored to your role.'
        },
        {
          icon:introSlideFiveFeatureIconTwo,
          text:'Receive instant feedback on tone, pace, and content.'
        },
        {
          icon:introSlideFiveFeatureIconThree,
          text:'Book mock interviews anytime, on your schedule.'
        }
      ]
    },

  ];
  return (
    <IntroSliderComponent
      onDone={onDone}
      onSkip={onDone}
      pages={pages}
      showDoneButtonOnLastPage={false}
    />
  );
};

const mapPropsToState = {
  setIsFirstTime: setIsFirstVisitAction,
};
const mapStateToProps = (state: IApplicationState) => {
  return {
    isFirstVisit: state.isFirstVisit,
  };
};

const connectedIntroSliderContainer = connect(
  mapStateToProps,
  mapPropsToState,
)(IntroSliderContainer);
export {connectedIntroSliderContainer as IntroSliderContainer};
