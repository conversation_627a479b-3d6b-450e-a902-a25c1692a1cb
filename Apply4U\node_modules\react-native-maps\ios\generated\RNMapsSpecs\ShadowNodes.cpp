
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeCpp.js
 */

#include "ShadowNodes.h"

namespace facebook::react {

extern const char RNMapsCalloutComponentName[] = "RNMapsCallout";
extern const char RNMapsCircleComponentName[] = "RNMapsCircle";
extern const char RNMapsGoogleMapViewComponentName[] = "RNMapsGoogleMapView";
extern const char RNMapsGooglePolygonComponentName[] = "RNMapsGooglePolygon";
extern const char RNMapsMapViewComponentName[] = "RNMapsMapView";
extern const char RNMapsMarkerComponentName[] = "RNMapsMarker";
extern const char RNMapsOverlayComponentName[] = "RNMapsOverlay";
extern const char RNMapsPolylineComponentName[] = "RNMapsPolyline";
extern const char RNMapsUrlTileComponentName[] = "RNMapsUrlTile";
extern const char RNMapsWMSTileComponentName[] = "RNMapsWMSTile";

} // namespace facebook::react
