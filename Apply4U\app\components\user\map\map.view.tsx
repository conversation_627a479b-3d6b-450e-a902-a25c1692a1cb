import React from 'react';
import {
  StyleSheet,
  View,
  SafeAreaView,
  TouchableOpacity,
  Text,
} from 'react-native';
import RN<PERSON>apView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { useTranslation } from 'react-i18next';
import { ITheme, useCurrentTheme } from '../../../theme';

interface IProps {
  goBack: () => void;
}

export const MapScreenView = (props: IProps) => {
  const { t } = useTranslation(['Map']);
  const styles = useCurrentTheme(createStyles);

  const initialRegion = {
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={props.goBack}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Map View</Text>
      </View>
      
      <RNMapView
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        initialRegion={initialRegion}
        showsUserLocation={true}
        showsMyLocationButton={true}
        zoomEnabled={true}
        scrollEnabled={true}
      >
        <Marker
          coordinate={{
            latitude: 37.78825,
            longitude: -122.4324,
          }}
          title="Sample Location"
          description="This is a sample marker"
        />
      </RNMapView>
    </SafeAreaView>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.backgroundPalette.primary,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: theme.spacing(5),
      backgroundColor: theme.palette.primary,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    backButton: {
      padding: theme.spacing(2.5),
      marginRight: theme.spacing(5),
    },
    backButtonText: {
      color: theme.textPalette.primary,
      fontSize: 16,
      fontWeight: 'bold',
    },
    headerTitle: {
      color: theme.textPalette.primary,
      fontSize: 18,
      fontWeight: 'bold',
      flex: 1,
      textAlign: 'center',
      marginRight: theme.spacing(12.5), // To center the title accounting for back button
    },
    map: {
      flex: 1,
    },
  });

  return styles;
};
