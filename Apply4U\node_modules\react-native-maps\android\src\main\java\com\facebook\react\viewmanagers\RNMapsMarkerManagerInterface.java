/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ReadableMap;

public interface RNMapsMarkerManagerInterface<T extends View> {
  void setAnchor(T view, @Nullable ReadableMap value);
  void setCalloutAnchor(T view, @Nullable ReadableMap value);
  void setImage(T view, @Nullable ReadableMap value);
  void setCalloutOffset(T view, @Nullable ReadableMap value);
  void setDisplayPriority(T view, @Nullable String value);
  void setCoordinate(T view, @Nullable ReadableMap value);
  void setDescription(T view, @Nullable String value);
  void setDraggable(T view, boolean value);
  void setTitle(T view, @Nullable String value);
  void setIdentifier(T view, @Nullable String value);
  void setIsPreselected(T view, boolean value);
  void setOpacity(T view, double value);
  void setPinColor(T view, @Nullable Integer value);
  void setTitleVisibility(T view, @Nullable String value);
  void setSubtitleVisibility(T view, @Nullable String value);
  void setUseLegacyPinView(T view, boolean value);
  void animateToCoordinates(T view, double latitude, double longitude, int duration);
  void setCoordinates(T view, double latitude, double longitude);
  void showCallout(T view);
  void hideCallout(T view);
  void redrawCallout(T view);
  void redraw(T view);
}
