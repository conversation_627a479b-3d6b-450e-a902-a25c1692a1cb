
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsCpp.js
 */

#include "Props.h"
#include <react/renderer/components/image/conversions.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>

namespace facebook::react {

RNMapsCalloutProps::RNMapsCalloutProps(
    const PropsParserContext &context,
    const RNMapsCalloutProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    alphaHitTest(convertRawProp(context, rawProps, "alphaHitTest", sourceProps.alphaHitTest, {false})),
    tooltip(convertRawProp(context, rawProps, "tooltip", sourceProps.tooltip, {false}))
      {}
RNMapsCircleProps::RNMapsCircleProps(
    const PropsParserContext &context,
    const RNMapsCircleProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    center(convertRawProp(context, rawProps, "center", sourceProps.center, {})),
    fillColor(convertRawProp(context, rawProps, "fillColor", sourceProps.fillColor, {})),
    radius(convertRawProp(context, rawProps, "radius", sourceProps.radius, {0.0})),
    strokeColor(convertRawProp(context, rawProps, "strokeColor", sourceProps.strokeColor, {})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {0.0})),
    tappable(convertRawProp(context, rawProps, "tappable", sourceProps.tappable, {false}))
      {}
RNMapsGoogleMapViewProps::RNMapsGoogleMapViewProps(
    const PropsParserContext &context,
    const RNMapsGoogleMapViewProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    camera(convertRawProp(context, rawProps, "camera", sourceProps.camera, {})),
    initialCamera(convertRawProp(context, rawProps, "initialCamera", sourceProps.initialCamera, {})),
    initialRegion(convertRawProp(context, rawProps, "initialRegion", sourceProps.initialRegion, {})),
    kmlSrc(convertRawProp(context, rawProps, "kmlSrc", sourceProps.kmlSrc, {})),
    googleMapId(convertRawProp(context, rawProps, "googleMapId", sourceProps.googleMapId, {})),
    loadingBackgroundColor(convertRawProp(context, rawProps, "loadingBackgroundColor", sourceProps.loadingBackgroundColor, {})),
    mapPadding(convertRawProp(context, rawProps, "mapPadding", sourceProps.mapPadding, {})),
    mapType(convertRawProp(context, rawProps, "mapType", sourceProps.mapType, {RNMapsGoogleMapViewMapType::Standard})),
    maxZoom(convertRawProp(context, rawProps, "maxZoom", sourceProps.maxZoom, {0.0})),
    minZoom(convertRawProp(context, rawProps, "minZoom", sourceProps.minZoom, {0.0})),
    paddingAdjustmentBehavior(convertRawProp(context, rawProps, "paddingAdjustmentBehavior", sourceProps.paddingAdjustmentBehavior, {RNMapsGoogleMapViewPaddingAdjustmentBehavior::Never})),
    pitchEnabled(convertRawProp(context, rawProps, "pitchEnabled", sourceProps.pitchEnabled, {true})),
    region(convertRawProp(context, rawProps, "region", sourceProps.region, {})),
    rotateEnabled(convertRawProp(context, rawProps, "rotateEnabled", sourceProps.rotateEnabled, {true})),
    scrollDuringRotateOrZoomEnabled(convertRawProp(context, rawProps, "scrollDuringRotateOrZoomEnabled", sourceProps.scrollDuringRotateOrZoomEnabled, {true})),
    scrollEnabled(convertRawProp(context, rawProps, "scrollEnabled", sourceProps.scrollEnabled, {true})),
    showsBuildings(convertRawProp(context, rawProps, "showsBuildings", sourceProps.showsBuildings, {true})),
    showsCompass(convertRawProp(context, rawProps, "showsCompass", sourceProps.showsCompass, {true})),
    showsIndoorLevelPicker(convertRawProp(context, rawProps, "showsIndoorLevelPicker", sourceProps.showsIndoorLevelPicker, {false})),
    showsIndoors(convertRawProp(context, rawProps, "showsIndoors", sourceProps.showsIndoors, {true})),
    showsMyLocationButton(convertRawProp(context, rawProps, "showsMyLocationButton", sourceProps.showsMyLocationButton, {true})),
    showsPointsOfInterest(convertRawProp(context, rawProps, "showsPointsOfInterest", sourceProps.showsPointsOfInterest, {false})),
    showsScale(convertRawProp(context, rawProps, "showsScale", sourceProps.showsScale, {false})),
    showsTraffic(convertRawProp(context, rawProps, "showsTraffic", sourceProps.showsTraffic, {false})),
    showsUserLocation(convertRawProp(context, rawProps, "showsUserLocation", sourceProps.showsUserLocation, {false})),
    userInterfaceStyle(convertRawProp(context, rawProps, "userInterfaceStyle", sourceProps.userInterfaceStyle, {RNMapsGoogleMapViewUserInterfaceStyle::System})),
    customMapStyleString(convertRawProp(context, rawProps, "customMapStyleString", sourceProps.customMapStyleString, {})),
    userLocationCalloutEnabled(convertRawProp(context, rawProps, "userLocationCalloutEnabled", sourceProps.userLocationCalloutEnabled, {false})),
    userLocationFastestInterval(convertRawProp(context, rawProps, "userLocationFastestInterval", sourceProps.userLocationFastestInterval, {0})),
    userLocationPriority(convertRawProp(context, rawProps, "userLocationPriority", sourceProps.userLocationPriority, {RNMapsGoogleMapViewUserLocationPriority::High})),
    userLocationUpdateInterval(convertRawProp(context, rawProps, "userLocationUpdateInterval", sourceProps.userLocationUpdateInterval, {0})),
    zoomControlEnabled(convertRawProp(context, rawProps, "zoomControlEnabled", sourceProps.zoomControlEnabled, {false})),
    zoomEnabled(convertRawProp(context, rawProps, "zoomEnabled", sourceProps.zoomEnabled, {true})),
    zoomTapEnabled(convertRawProp(context, rawProps, "zoomTapEnabled", sourceProps.zoomTapEnabled, {true}))
      {}
RNMapsGooglePolygonProps::RNMapsGooglePolygonProps(
    const PropsParserContext &context,
    const RNMapsGooglePolygonProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    coordinates(convertRawProp(context, rawProps, "coordinates", sourceProps.coordinates, {})),
    fillColor(convertRawProp(context, rawProps, "fillColor", sourceProps.fillColor, {})),
    strokeColor(convertRawProp(context, rawProps, "strokeColor", sourceProps.strokeColor, {})),
    geodesic(convertRawProp(context, rawProps, "geodesic", sourceProps.geodesic, {false})),
    holes(convertRawProp(context, rawProps, "holes", sourceProps.holes, {})),
    tappable(convertRawProp(context, rawProps, "tappable", sourceProps.tappable, {false}))
      {}
RNMapsMapViewProps::RNMapsMapViewProps(
    const PropsParserContext &context,
    const RNMapsMapViewProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    cacheEnabled(convertRawProp(context, rawProps, "cacheEnabled", sourceProps.cacheEnabled, {false})),
    camera(convertRawProp(context, rawProps, "camera", sourceProps.camera, {})),
    compassOffset(convertRawProp(context, rawProps, "compassOffset", sourceProps.compassOffset, {})),
    followsUserLocation(convertRawProp(context, rawProps, "followsUserLocation", sourceProps.followsUserLocation, {false})),
    poiClickEnabled(convertRawProp(context, rawProps, "poiClickEnabled", sourceProps.poiClickEnabled, {false})),
    initialCamera(convertRawProp(context, rawProps, "initialCamera", sourceProps.initialCamera, {})),
    initialRegion(convertRawProp(context, rawProps, "initialRegion", sourceProps.initialRegion, {})),
    kmlSrc(convertRawProp(context, rawProps, "kmlSrc", sourceProps.kmlSrc, {})),
    legalLabelInsets(convertRawProp(context, rawProps, "legalLabelInsets", sourceProps.legalLabelInsets, {})),
    liteMode(convertRawProp(context, rawProps, "liteMode", sourceProps.liteMode, {false})),
    googleMapId(convertRawProp(context, rawProps, "googleMapId", sourceProps.googleMapId, {})),
    googleRenderer(convertRawProp(context, rawProps, "googleRenderer", sourceProps.googleRenderer, {RNMapsMapViewGoogleRenderer::LATEST})),
    loadingBackgroundColor(convertRawProp(context, rawProps, "loadingBackgroundColor", sourceProps.loadingBackgroundColor, {})),
    loadingEnabled(convertRawProp(context, rawProps, "loadingEnabled", sourceProps.loadingEnabled, {false})),
    loadingIndicatorColor(convertRawProp(context, rawProps, "loadingIndicatorColor", sourceProps.loadingIndicatorColor, {})),
    mapPadding(convertRawProp(context, rawProps, "mapPadding", sourceProps.mapPadding, {})),
    mapType(convertRawProp(context, rawProps, "mapType", sourceProps.mapType, {RNMapsMapViewMapType::Standard})),
    maxDelta(convertRawProp(context, rawProps, "maxDelta", sourceProps.maxDelta, {0.0})),
    maxZoom(convertRawProp(context, rawProps, "maxZoom", sourceProps.maxZoom, {0.0})),
    minDelta(convertRawProp(context, rawProps, "minDelta", sourceProps.minDelta, {0.0})),
    minZoom(convertRawProp(context, rawProps, "minZoom", sourceProps.minZoom, {0.0})),
    moveOnMarkerPress(convertRawProp(context, rawProps, "moveOnMarkerPress", sourceProps.moveOnMarkerPress, {true})),
    handlePanDrag(convertRawProp(context, rawProps, "handlePanDrag", sourceProps.handlePanDrag, {false})),
    paddingAdjustmentBehavior(convertRawProp(context, rawProps, "paddingAdjustmentBehavior", sourceProps.paddingAdjustmentBehavior, {RNMapsMapViewPaddingAdjustmentBehavior::Never})),
    pitchEnabled(convertRawProp(context, rawProps, "pitchEnabled", sourceProps.pitchEnabled, {true})),
    region(convertRawProp(context, rawProps, "region", sourceProps.region, {})),
    rotateEnabled(convertRawProp(context, rawProps, "rotateEnabled", sourceProps.rotateEnabled, {true})),
    scrollDuringRotateOrZoomEnabled(convertRawProp(context, rawProps, "scrollDuringRotateOrZoomEnabled", sourceProps.scrollDuringRotateOrZoomEnabled, {true})),
    scrollEnabled(convertRawProp(context, rawProps, "scrollEnabled", sourceProps.scrollEnabled, {true})),
    showsBuildings(convertRawProp(context, rawProps, "showsBuildings", sourceProps.showsBuildings, {true})),
    showsCompass(convertRawProp(context, rawProps, "showsCompass", sourceProps.showsCompass, {true})),
    showsIndoorLevelPicker(convertRawProp(context, rawProps, "showsIndoorLevelPicker", sourceProps.showsIndoorLevelPicker, {false})),
    showsIndoors(convertRawProp(context, rawProps, "showsIndoors", sourceProps.showsIndoors, {true})),
    showsMyLocationButton(convertRawProp(context, rawProps, "showsMyLocationButton", sourceProps.showsMyLocationButton, {true})),
    showsScale(convertRawProp(context, rawProps, "showsScale", sourceProps.showsScale, {false})),
    showsUserLocation(convertRawProp(context, rawProps, "showsUserLocation", sourceProps.showsUserLocation, {false})),
    tintColor(convertRawProp(context, rawProps, "tintColor", sourceProps.tintColor, {})),
    toolbarEnabled(convertRawProp(context, rawProps, "toolbarEnabled", sourceProps.toolbarEnabled, {true})),
    userInterfaceStyle(convertRawProp(context, rawProps, "userInterfaceStyle", sourceProps.userInterfaceStyle, {RNMapsMapViewUserInterfaceStyle::System})),
    customMapStyleString(convertRawProp(context, rawProps, "customMapStyleString", sourceProps.customMapStyleString, {})),
    userLocationAnnotationTitle(convertRawProp(context, rawProps, "userLocationAnnotationTitle", sourceProps.userLocationAnnotationTitle, {})),
    userLocationCalloutEnabled(convertRawProp(context, rawProps, "userLocationCalloutEnabled", sourceProps.userLocationCalloutEnabled, {false})),
    userLocationFastestInterval(convertRawProp(context, rawProps, "userLocationFastestInterval", sourceProps.userLocationFastestInterval, {5000})),
    userLocationPriority(convertRawProp(context, rawProps, "userLocationPriority", sourceProps.userLocationPriority, {RNMapsMapViewUserLocationPriority::High})),
    userLocationUpdateInterval(convertRawProp(context, rawProps, "userLocationUpdateInterval", sourceProps.userLocationUpdateInterval, {5000})),
    zoomControlEnabled(convertRawProp(context, rawProps, "zoomControlEnabled", sourceProps.zoomControlEnabled, {true})),
    zoomEnabled(convertRawProp(context, rawProps, "zoomEnabled", sourceProps.zoomEnabled, {true})),
    showsTraffic(convertRawProp(context, rawProps, "showsTraffic", sourceProps.showsTraffic, {false})),
    zoomTapEnabled(convertRawProp(context, rawProps, "zoomTapEnabled", sourceProps.zoomTapEnabled, {true})),
    cameraZoomRange(convertRawProp(context, rawProps, "cameraZoomRange", sourceProps.cameraZoomRange, {}))
      {}
RNMapsMarkerProps::RNMapsMarkerProps(
    const PropsParserContext &context,
    const RNMapsMarkerProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    anchor(convertRawProp(context, rawProps, "anchor", sourceProps.anchor, {})),
    calloutAnchor(convertRawProp(context, rawProps, "calloutAnchor", sourceProps.calloutAnchor, {})),
    image(convertRawProp(context, rawProps, "image", sourceProps.image, {})),
    calloutOffset(convertRawProp(context, rawProps, "calloutOffset", sourceProps.calloutOffset, {})),
    displayPriority(convertRawProp(context, rawProps, "displayPriority", sourceProps.displayPriority, {RNMapsMarkerDisplayPriority::Required})),
    coordinate(convertRawProp(context, rawProps, "coordinate", sourceProps.coordinate, {})),
    description(convertRawProp(context, rawProps, "description", sourceProps.description, {})),
    draggable(convertRawProp(context, rawProps, "draggable", sourceProps.draggable, {false})),
    title(convertRawProp(context, rawProps, "title", sourceProps.title, {})),
    identifier(convertRawProp(context, rawProps, "identifier", sourceProps.identifier, {})),
    isPreselected(convertRawProp(context, rawProps, "isPreselected", sourceProps.isPreselected, {false})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    pinColor(convertRawProp(context, rawProps, "pinColor", sourceProps.pinColor, {})),
    titleVisibility(convertRawProp(context, rawProps, "titleVisibility", sourceProps.titleVisibility, {RNMapsMarkerTitleVisibility::Visible})),
    subtitleVisibility(convertRawProp(context, rawProps, "subtitleVisibility", sourceProps.subtitleVisibility, {RNMapsMarkerSubtitleVisibility::Adaptive})),
    useLegacyPinView(convertRawProp(context, rawProps, "useLegacyPinView", sourceProps.useLegacyPinView, {false}))
      {}
RNMapsOverlayProps::RNMapsOverlayProps(
    const PropsParserContext &context,
    const RNMapsOverlayProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    bearing(convertRawProp(context, rawProps, "bearing", sourceProps.bearing, {0.0})),
    bounds(convertRawProp(context, rawProps, "bounds", sourceProps.bounds, {})),
    image(convertRawProp(context, rawProps, "image", sourceProps.image, {})),
    opacity(convertRawProp(context, rawProps, "opacity", sourceProps.opacity, {1.0})),
    tappable(convertRawProp(context, rawProps, "tappable", sourceProps.tappable, {false}))
      {}
RNMapsPolylineProps::RNMapsPolylineProps(
    const PropsParserContext &context,
    const RNMapsPolylineProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    coordinates(convertRawProp(context, rawProps, "coordinates", sourceProps.coordinates, {})),
    geodesic(convertRawProp(context, rawProps, "geodesic", sourceProps.geodesic, {false})),
    lineCap(convertRawProp(context, rawProps, "lineCap", sourceProps.lineCap, {RNMapsPolylineLineCap::Butt})),
    lineDashPattern(convertRawProp(context, rawProps, "lineDashPattern", sourceProps.lineDashPattern, {})),
    lineJoin(convertRawProp(context, rawProps, "lineJoin", sourceProps.lineJoin, {RNMapsPolylineLineJoin::Miter})),
    strokeColor(convertRawProp(context, rawProps, "strokeColor", sourceProps.strokeColor, {})),
    strokeColors(convertRawProp(context, rawProps, "strokeColors", sourceProps.strokeColors, {})),
    strokeWidth(convertRawProp(context, rawProps, "strokeWidth", sourceProps.strokeWidth, {0.0})),
    tappable(convertRawProp(context, rawProps, "tappable", sourceProps.tappable, {false}))
      {}
RNMapsUrlTileProps::RNMapsUrlTileProps(
    const PropsParserContext &context,
    const RNMapsUrlTileProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    doubleTileSize(convertRawProp(context, rawProps, "doubleTileSize", sourceProps.doubleTileSize, {false})),
    flipY(convertRawProp(context, rawProps, "flipY", sourceProps.flipY, {false})),
    maximumNativeZ(convertRawProp(context, rawProps, "maximumNativeZ", sourceProps.maximumNativeZ, {100})),
    maximumZ(convertRawProp(context, rawProps, "maximumZ", sourceProps.maximumZ, {100})),
    minimumZ(convertRawProp(context, rawProps, "minimumZ", sourceProps.minimumZ, {0})),
    offlineMode(convertRawProp(context, rawProps, "offlineMode", sourceProps.offlineMode, {false})),
    shouldReplaceMapContent(convertRawProp(context, rawProps, "shouldReplaceMapContent", sourceProps.shouldReplaceMapContent, {false})),
    tileCacheMaxAge(convertRawProp(context, rawProps, "tileCacheMaxAge", sourceProps.tileCacheMaxAge, {0})),
    tileCachePath(convertRawProp(context, rawProps, "tileCachePath", sourceProps.tileCachePath, {})),
    tileSize(convertRawProp(context, rawProps, "tileSize", sourceProps.tileSize, {256})),
    urlTemplate(convertRawProp(context, rawProps, "urlTemplate", sourceProps.urlTemplate, {}))
      {}
RNMapsWMSTileProps::RNMapsWMSTileProps(
    const PropsParserContext &context,
    const RNMapsWMSTileProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    maximumNativeZ(convertRawProp(context, rawProps, "maximumNativeZ", sourceProps.maximumNativeZ, {100})),
    maximumZ(convertRawProp(context, rawProps, "maximumZ", sourceProps.maximumZ, {100})),
    minimumZ(convertRawProp(context, rawProps, "minimumZ", sourceProps.minimumZ, {0})),
    offlineMode(convertRawProp(context, rawProps, "offlineMode", sourceProps.offlineMode, {false})),
    shouldReplaceMapContent(convertRawProp(context, rawProps, "shouldReplaceMapContent", sourceProps.shouldReplaceMapContent, {false})),
    tileCacheMaxAge(convertRawProp(context, rawProps, "tileCacheMaxAge", sourceProps.tileCacheMaxAge, {0})),
    tileCachePath(convertRawProp(context, rawProps, "tileCachePath", sourceProps.tileCachePath, {})),
    tileSize(convertRawProp(context, rawProps, "tileSize", sourceProps.tileSize, {256})),
    urlTemplate(convertRawProp(context, rawProps, "urlTemplate", sourceProps.urlTemplate, {}))
      {}

} // namespace facebook::react
