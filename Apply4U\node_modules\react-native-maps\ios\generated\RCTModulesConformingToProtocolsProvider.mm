/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#import "RCTModulesConformingToProtocolsProvider.h"

@implementation RCTModulesConformingToProtocolsProvider

+(NSArray<NSString *> *)imageURLLoaderClassNames
{
  return @[
    
  ];
}

+(NSArray<NSString *> *)imageDataDecoderClassNames
{
  return @[
    
  ];
}

+(NSArray<NSString *> *)URLRequestHandlerClassNames
{
  return @[
    
  ];
}

@end
