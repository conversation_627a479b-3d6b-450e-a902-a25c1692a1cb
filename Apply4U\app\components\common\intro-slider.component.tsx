import React from 'react';
import {
  Image,
  SafeAreaView,
  ScrollView,
  Text,
  useWindowDimensions,
  View,
  StyleSheet,
  Pressable,
  Animated,
  Easing,
} from 'react-native';
import {IIntroSliderPage} from '../../interfaces/user/slider-page.interface';
import {ITheme, useCurrentTheme} from '../../theme';
import {hasValues} from '../../utility';
import {Button} from '../common';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faChevronRight} from '@fortawesome/free-solid-svg-icons';
import {SegmentedProgressCircle} from './segmented-progress-circle';

interface IProps {
  pages: IIntroSliderPage[];
  onSkip: () => void;
  onDone: () => void;
  showDoneButtonOnLastPage?: boolean;
}
export const IntroSliderComponent = (props: IProps) => {
  const [currentPage, setCurrentPage] = React.useState(0);
  const {width, height} = useWindowDimensions();
  const [dotAnimations, setDotAnimations] = React.useState<Animated.Value[]>([]);

  const setSliderPage = (event: any) => {
    const {x} = event.nativeEvent.contentOffset;
    const indexOfNextScreen = Math.round(x / width);
    if (indexOfNextScreen !== currentPage) {
      setCurrentPage(indexOfNextScreen);
    }
  };

  const [pages, setPages] = React.useState<IIntroSliderPage[]>(props.pages);
  React.useEffect(() => {
    setPages(props.pages);
    const animations = props.pages.map((_, i) => new Animated.Value(i === currentPage ? 1 : 0));
    setDotAnimations(animations);
  }, [props.pages]);
  React.useEffect(() => {
    dotAnimations.forEach((anim, index) => {
      Animated.timing(anim, {
        toValue: index === currentPage ? 1 : 0,
        duration: 300,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
        useNativeDriver: false,
      }).start();
    });
  }, [currentPage, dotAnimations]);
  const onNextClick = () => {
    if (currentPage < pages.length) {
      let nextPage = currentPage + 1;
      if (nextPage < pages.length) {
        let x = nextPage * width;

        if (scrollRef.current) {
          scrollRef.current.scrollTo({x: x, y: 0});
        }
      }
    }
  };

  const styles = useCurrentTheme(createStyles, {
    screenWidth: width,
    screenHeight: height,
  });

  const scrollRef = React.useRef<ScrollView | null>(null);
  return (
    <>
      <SafeAreaView style={styles.safeAreaContainer}>
        <View style={styles.containerView}>
          <View style={styles.mainContainer}>
            <ScrollView
              ref={scrollRef}
              horizontal={true}
              scrollEventThrottle={16}
              pagingEnabled={true}
              showsHorizontalScrollIndicator={false}
              onScroll={(event: any) => {
                setSliderPage(event);
              }}>
              {!!pages &&
                hasValues(pages) &&
                pages.map((page: IIntroSliderPage, index: number) => {
                  return (
                    <View key={`item_${index}`} style={styles.pageContainer}>
                      {!!page.image && (
                        <View style={styles.imageContainer}>
                          <Image
                            source={page.image}
                            style={styles.imageStyle}
                          />
                        </View>
                      )}
                      {(!!page.title || !!page.description) && (
                        <View style={styles.textContainer}>
                          {!!page.title && (
                            <View style={styles.textHeaderContainer}>
                              <Text style={page.titleStyle ? {...styles.smallerHeader, ...page.titleStyle} : styles.smallerHeader}>{page.title}</Text>
                            </View>
                          )}
                          {!!page.description && (
                            <View style={styles.textDescriptionContainer}>
                              <Text style={styles.paragraph}>
                                {page.description}
                              </Text>
                            </View>
                          )}
                          {!!page.featurePoints && page.featurePoints.length > 0 && (
                            <View style={styles.featurePointsContainer}>
                              {page.featurePoints.map((point, pointIndex) => (
                                <View key={`point_${pointIndex}`} style={styles.featurePointItem}>
                                  {(!!point.image || !!point.icon) && (
                                    <Image source={point.image || point.icon} style={styles.featurePointIcon} />
                                  )}
                                  <Text style={styles.featurePointText}>{point.text}</Text>
                                </View>
                              ))}
                            </View>
                          )}
                        </View>
                      )}
                    </View>
                  );
                })}
            </ScrollView>
            <View style={styles.doneButtonContainer}>
              {!!props.showDoneButtonOnLastPage &&
                props.showDoneButtonOnLastPage === true &&
                currentPage === pages.length - 1 && (
                  <Button
                    styles={styles.doneButtonStyle}
                    text={'Done'}
                    pressed={props.onDone}
                  />
                )}
            </View>
            <View style={styles.bottomContainer}>
              <View style={styles.bottomContainerRow1}>
                <Pressable onPress={props.onSkip} style={styles.skipButton}>
                  <Text style={styles.skipButtonText}>Skip</Text>
                </Pressable>
              </View>
              <View
                style={[styles.paginationWrapper, styles.bottomContainerRow2]}>
                {pages.map((_, index) => {
                  if (dotAnimations.length === 0) {
                    return (
                      <View
                        style={[
                          styles.paginationDots,
                          currentPage === index ? styles.activePaginationDot : styles.inactivePaginationDot,
                        ]}
                        key={index}
                      />
                    );
                  }
                  const dotWidth = dotAnimations[index].interpolate({
                    inputRange: [0, 1],
                    outputRange: [8, 24],
                  });

                  const backgroundColor = dotAnimations[index].interpolate({
                    inputRange: [0, 1],
                    outputRange: ['#D9D9D9', '#0F1E5A'],
                  });

                  return (
                    <Animated.View
                      key={index}
                      style={[
                        styles.paginationDots,
                        {
                          width: dotWidth,
                          backgroundColor,
                        },
                      ]}
                    />
                  );
                })}
              </View>
              <View style={styles.bottomContainerRow3}>
                {currentPage < pages.length - 1 && (
                  <View style={styles.nextButtonContainer}>
                    <SegmentedProgressCircle
                      progress={(currentPage + 1) / pages.length}
                      segmentCount={5}
                      radius={30}
                      strokeWidth={6}
                      activeColor="#0F1E5A"
                      inactiveColor="#D0E1FF"
                      bgColor="#F5F9FF">
                      <Pressable onPress={onNextClick} style={styles.nextButtonInner}>
                        <View style={styles.arrowContainer}>
                          <FontAwesomeIcon icon={faChevronRight} color="#FFFFFF" size={20} />
                        </View>
                      </Pressable>
                    </SegmentedProgressCircle>
                  </View>
                )}
                {/* {currentPage === pages.length - 1 && (
                  <Pressable onPress={props.onDone}>
                    <Text style={styles.bottomNavigationButton}>Done</Text>
                  </Pressable>
                )} */}
                {currentPage === pages.length - 1 && (
                  <View style={styles.nextButtonContainer}>
                    <SegmentedProgressCircle
                      progress={1}
                      segmentCount={5}
                      radius={30}
                      strokeWidth={6}
                      activeColor="#0F1E5A"
                      inactiveColor="#D0E1FF"
                      bgColor="#F5F9FF">
                      <Pressable onPress={props.onDone} style={styles.nextButtonInner}>
                        <View style={styles.arrowContainer}>
                          <FontAwesomeIcon icon={faChevronRight} color="#FFFFFF" size={20} />
                        </View>
                      </Pressable>
                    </SegmentedProgressCircle>
                  </View>
                )}
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </>
  );
};

const createStyles = (theme: ITheme, dimensions: any) =>
  StyleSheet.create({
    safeAreaContainer: {
      flex: 1,
      backgroundColor: theme.palette.white,
    },
    containerView: {
      flex: 1,
      backgroundColor: theme.palette.white,
    },
    mainContainer: {flex: 1, backgroundColor: theme.palette.white},
    doneButtonContainer: {
      width: '100%',
      justifyContent: 'flex-start',

    },
    bottomContainer: {
      flexDirection: 'row',
      height: 90,
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    pageContainer: {
      width: dimensions.screenWidth,
      height: dimensions.screenHeight - 110,
      flex: 1,
      backgroundColor: theme.palette.white,
    },
    imageContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing(1),
    },
    textContainer: {
      flex: 1,
      justifyContent: 'flex-start',
      paddingTop: theme.spacing(15),
    },
    textHeaderContainer: {
      justifyContent: 'center',
      marginBottom: theme.spacing(2),
    },
    textDescriptionContainer: {
      justifyContent: 'center',
      marginBottom: theme.spacing(2),
    },
    bottomContainerRow1: {
      flex: 1,
      justifyContent: 'center',
    },
    bottomContainerRow2: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    bottomContainerRow3: {
      flex: 1,
      justifyContent: 'center',
    },
    imageStyle: {
      height: 350,
      width: 350,
      alignSelf: 'center',
      resizeMode: 'contain',
      marginTop: theme.spacing(1),
    },

    header: {
      ...theme.typography.bold.large,
      fontSize: 30,
      alignSelf: 'center',
      color: theme.palette.black,
      textAlign: 'center',
      marginBottom: theme.spacing(2),
    },
    smallerHeader: {
      alignSelf: 'center',
      color: theme.palette.black,
      textAlign: 'center',
      marginBottom: theme.spacing(2),
      paddingHorizontal: theme.spacing(5),
    },
    paragraph: {
      fontFamily: 'Poppins-Medium',
      color: theme.palette.black,
      paddingLeft: theme.spacing(15),
      paddingRight: theme.spacing(15),
      textAlign: 'center',
      marginBottom: theme.spacing(5),
      fontSize: 16,
    },
    paginationWrapper: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    paginationDots: {
      height: 8,
      width: 8,
      borderRadius: 4,
      backgroundColor: theme.palette.primary,
      marginLeft: theme.spacing(5),
    },
    activePaginationDot: {
      width: 24,
      backgroundColor: theme.palette.primary,
      height: 8,
      borderRadius: 4,
    },
    inactivePaginationDot: {
      backgroundColor: '#D9D9D9',
    },
    doneButtonStyle: {
      width: '90%',
      alignSelf: 'center',
      borderRadius: 0,
      backgroundColor: theme.palette.secondary,
    },
    skipButton: {
      backgroundColor: '#D9D9D9',
      paddingVertical: 10,
      paddingHorizontal: 30,
      borderRadius: 5,
      marginLeft: 10,
      alignSelf: 'center',
    },
    skipButtonText: {
      ...theme.typography.bold.small,
      color: '#0E1B5D',
    },
    bottomNavigationButton: {
      ...theme.typography.bold.medium,
      color: theme.palette.primary,
      alignSelf: 'center',
      marginLeft: theme.spacing(10),
    },
    nextButtonContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.spacing(5),
    },
    nextButtonInner: {
      width: '100%',
      height: '100%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    arrowContainer: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: '#0F1E5A',
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: 'white',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 1,
    },
    featurePointsContainer: {
      flex: 3,
      paddingHorizontal: theme.spacing(10),
      marginTop: theme.spacing(20),
    },
    featurePointItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: theme.spacing(10),
    },
    featurePointIcon: {
      width: 42,
      height: 42,
      marginRight: theme.spacing(8),
      resizeMode: 'contain',
      marginTop: theme.spacing(1),
    },
    featurePointText: {
      fontFamily: 'Poppins-Medium',
      color: theme.palette.black,
      flex: 1,
      paddingTop: theme.spacing(0),
      fontSize: 15,

    },

  });