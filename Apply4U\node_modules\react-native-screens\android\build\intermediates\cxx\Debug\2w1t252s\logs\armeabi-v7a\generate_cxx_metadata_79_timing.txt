# C/C++ build system timings
generate_cxx_metadata
  [gap of 26ms]
  create-invalidation-state 47ms
  [gap of 15ms]
generate_cxx_metadata completed in 88ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 35ms
  [gap of 14ms]
generate_cxx_metadata completed in 65ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 33ms
  [gap of 12ms]
generate_cxx_metadata completed in 58ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 26ms
generate_cxx_metadata completed in 49ms

# C/C++ build system timings
generate_cxx_metadata 24ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 40ms
  write-metadata-json-to-file 24ms
generate_cxx_metadata completed in 94ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 26ms]
  create-invalidation-state 55ms
  [gap of 15ms]
  write-metadata-json-to-file 26ms
generate_cxx_metadata completed in 125ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 69ms
  [gap of 11ms]
generate_cxx_metadata completed in 107ms

