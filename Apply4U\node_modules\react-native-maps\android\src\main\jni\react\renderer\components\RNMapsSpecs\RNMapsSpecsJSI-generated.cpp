/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNMapsSpecsJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeAirMapsModuleCxxSpecJSI_getCamera(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAirMapsModuleCxxSpecJSI *>(&turboModule)->getCamera(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
}
static jsi::Value __hostFunction_NativeAirMapsModuleCxxSpecJSI_getMarkersFrames(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAirMapsModuleCxxSpecJSI *>(&turboModule)->getMarkersFrames(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asBool()
  );
}
static jsi::Value __hostFunction_NativeAirMapsModuleCxxSpecJSI_getMapBoundaries(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAirMapsModuleCxxSpecJSI *>(&turboModule)->getMapBoundaries(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber()
  );
}
static jsi::Value __hostFunction_NativeAirMapsModuleCxxSpecJSI_takeSnapshot(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAirMapsModuleCxxSpecJSI *>(&turboModule)->takeSnapshot(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asString(rt)
  );
}
static jsi::Value __hostFunction_NativeAirMapsModuleCxxSpecJSI_getAddressFromCoordinates(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAirMapsModuleCxxSpecJSI *>(&turboModule)->getAddressFromCoordinates(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeAirMapsModuleCxxSpecJSI_getPointForCoordinate(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAirMapsModuleCxxSpecJSI *>(&turboModule)->getPointForCoordinate(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}
static jsi::Value __hostFunction_NativeAirMapsModuleCxxSpecJSI_getCoordinateForPoint(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAirMapsModuleCxxSpecJSI *>(&turboModule)->getCoordinateForPoint(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asNumber(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt)
  );
}

NativeAirMapsModuleCxxSpecJSI::NativeAirMapsModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNMapsAirModule", jsInvoker) {
  methodMap_["getCamera"] = MethodMetadata {1, __hostFunction_NativeAirMapsModuleCxxSpecJSI_getCamera};
  methodMap_["getMarkersFrames"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleCxxSpecJSI_getMarkersFrames};
  methodMap_["getMapBoundaries"] = MethodMetadata {1, __hostFunction_NativeAirMapsModuleCxxSpecJSI_getMapBoundaries};
  methodMap_["takeSnapshot"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleCxxSpecJSI_takeSnapshot};
  methodMap_["getAddressFromCoordinates"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleCxxSpecJSI_getAddressFromCoordinates};
  methodMap_["getPointForCoordinate"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleCxxSpecJSI_getPointForCoordinate};
  methodMap_["getCoordinateForPoint"] = MethodMetadata {2, __hostFunction_NativeAirMapsModuleCxxSpecJSI_getCoordinateForPoint};
}


} // namespace facebook::react
